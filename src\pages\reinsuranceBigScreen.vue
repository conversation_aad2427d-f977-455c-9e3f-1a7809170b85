<template>
  <div
    class="big-screen-container"
    style="height: 100%;
    overflow: hidden;
    position: relative;"
  >
    <!-- 顶部标题栏（编辑模式下显示输入框） -->
    <div class="header-long bg-black">
      <span class="header-right-middle">
        <el-input
          v-if="editMode && setUpType == '组件'"
          v-model="configEditing.title"
          placeholder="请输入标题"
          size="small"
          style="width: 25%;"
        />
        <span v-else>{{ basicConf.title }}</span>
      </span>
      <div class="header-right-time">
        {{ currentTime }}
      </div>
    </div>
    <div
      v-if="editMode"
      style="position: fixed;
      width: 100%;
      height: 70Px;
      margin-top: 70Px;
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;"
    >
      <div class="buttonZ1">
        <span 
          id="buttonSpanId1" 
          class="buttonSpanCur" 
          @click="changeScene(posDataId, '站点')"
        >重保场景-站点设置</span>
      </div>
      <div class="buttonZ2">
        <span 
          id="buttonSpanId4" 
          class="buttonSpanCur" 
          @click="changeScene(lineDataId, '线路')"
        >重保场景-线路设置</span>
      </div>
      <div class="buttonZ3">
        <span 
          id="buttonSpanId2" 
          class="buttonSpanCur" 
          @click="changeScene(3, '组件')"
        >大屏显示模块设置</span>
      </div>
    </div>
    <!-- 切换按钮 -->
    <!-- v-show="editMode && setUpType == '组件'" -->
    <div
      v-show="editMode ? (editMode && setUpType == '组件') : true"
      :style="{
        position: 'fixed',
        width: '100%',
        height: '70Px',
        marginTop: editMode ? '140Px' : '70Px',
        zTndex: '2',
        display: ' flex',
        justifyContent: 'center',
        alignItems: 'center'
      }" 
      style="z-index:2"
    >
      <div 
        v-for="item in screenDataList" 
        :key="item.orderNo" 
        class="buttonZ1 bg-black" 
        @click="changeMenu(item)"
        @contextmenu.prevent="openMenu($event, item)"
      >
        <el-input 
          v-if="editMode && setUpType == '组件'" 
          v-model="item.screenName" 
        />
        <span 
          v-else 
          id="buttonSpanId1" 
          class="buttonSpanCur" 
          @click="changeSceneList(item)"
        >{{ item.screenName }}</span>
      </div>
      <ul 
        v-show="visible" 
        :style="{ left: left + 'px', top: top + 'px' }" 
        class="contextmenu"
      >
        <li @click="copyItem(rightClickItem)">
          复制
        </li>
        <li @click="deleteItem(rightClickItem)">
          删除
        </li>
      </ul>
    </div>
    <!-- 编辑控制按钮 -->
    <div 
      v-show="editMode && setUpType == '组件'" 
      style="position: absolute; top: 5%; right: 1%; z-index: 2;"
    >
      <el-button 
        v-if="editMode" 
        size="small" 
        type="primary" 
        @click="saveConfig"
      > 
        保存
      </el-button>
      <el-button 
        v-if="editMode" 
        size="small" 
        @click="cancelEdit"
      > 
        取消
      </el-button>
    </div>

    <!-- 线路列表框 -->
    <div 
      v-show="editMode && setUpType == '站点'" 
      class="line-list-container" 
      style="width: 25%;
        height: 60%;
        position: absolute;
        top: 8%;
        right: 1%;
        z-index: 2;
      "
    >
      <div
        class="search-container"
        style="height: 50px;"
      >
        <el-button
          v-if="!addRouter"
          size="small"
          type="primary"
          style="float: right;"
          @click="openRouter"
        >
          新增线路/站点
        </el-button>
      </div>
      <el-form
        v-if="addRouter"
        ref="form"
        :model="routerForm"
        size="mini"
        class="route-naming"
      >
        <el-form-item label="起点">
          <el-input
            v-model="routerForm.startPoint.pointName"
            placeholder="请输入搜索关键词"
            clearable
            @focus="openMapClicks(1)"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="searchLocation(routerForm.startPoint.pointName, 'startPoint')"
            />
          </el-input>
          <!-- 下拉框 -->
          <div v-if="showDropdown === 'startPoint'" class="address-dropdown" v-click-outside="handleClickOutside">
            <div v-for="(item, index) in addressList" :key="index" class="dropdown-item"
              @click="selectAddress('startPoint', item)">
              {{ item.address }}
            </div>
          </div>
          <el-button type="primary" style="margin-left: 1%;" :disabled="!routerForm.startPoint.pointName"
            @click="openSetupPoint('起点', routerForm.startPoint)">
            设置站点
          </el-button>
        </el-form-item>

        <el-form-item label="终点">
          <el-input v-model="routerForm.endPoint.pointName" placeholder="请输入搜索关键词" clearable @focus="openMapClicks(2)">
            <i slot="suffix" class="el-input__icon el-icon-search"
              @click="searchLocation(routerForm.endPoint.pointName, 'endPoint')" />
          </el-input>
          <!-- 下拉框 -->
          <div v-if="showDropdown === 'endPoint'" class="address-dropdown" v-click-outside="handleClickOutside">
            <div v-for="(item, index) in addressList" :key="index" class="dropdown-item"
              @click="selectAddress('endPoint', item)">
              {{ item.address }}
            </div>
          </div>
          <el-button type="primary" style="margin-left: 1%;" :disabled="!routerForm.endPoint.pointName"
            @click="openSetupPoint('终点', routerForm.endPoint)">
            设置站点
          </el-button>
        </el-form-item>

        <el-form-item label="线路">
          <el-input v-model="routerForm.lineName" placeholder="请输入保障线路名称" clearable />
          <el-button type="primary" style="margin-left: 1%;" :disabled="!routerForm.lineName"
            @click="openSetupPoint('线路', routerForm)">
            设置线路
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
            :disabled="routerForm.startPoint.longitude == '' && routerForm.endPoint.longitude == ''"
            @click="addRouters">
            立即创建
          </el-button>
          <el-button @click="cancelAdd">
            取消
          </el-button>
        </el-form-item>
      </el-form>
      <div v-if="!addRouter" class="back_table">
        <el-table :data="tableData" stripe height="300" style="width: 100%;">
          <el-table-column prop="lineName" label="线路名称" />
          <el-table-column prop="startPos" label="起点">
            <template slot-scope="scope">
              {{ scope.row.startPoint.pointAlias || '' }}
            </template>
          </el-table-column>
          <el-table-column prop="endPos" label="终点">
            <template slot-scope="scope">
              {{ scope.row.endPoint ? scope.row.endPoint.pointAlias : '' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editRouter(scope.row)">
                编辑
              </el-button>
              <el-button type="text" size="small" @click="deleteRouter(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 站点设置 -->
    <el-dialog :title="pointsType" :visible.sync="setupPointDialog" width="35vw" closable @close="cancelSiteChanges">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="起点信息" name="startPoint" />
        <el-tab-pane label="终点信息" name="endPoint" />
        <el-tab-pane label="线路信息" name="line" />
      </el-tabs>
      <el-form v-if="activeName == 'startPoint'" :model="routerForm.startPoint" size="mini">
        <el-form-item label="站点别名" :label-width="formLabelWidth">
          <el-input v-model="routerForm.startPoint.pointAlias" autocomplete="off" />
        </el-form-item>
        <el-form-item label="站点位置" :label-width="formLabelWidth">
          <el-input v-model="routerForm.startPoint.pointName" autocomplete="off" disabled />
        </el-form-item>
        <el-form-item label="站点精度" :label-width="formLabelWidth">
          <el-input v-model="routerForm.startPoint.longitude" autocomplete="off" disabled />
        </el-form-item>
        <el-form-item label="站点维度" :label-width="formLabelWidth">
          <el-input v-model="routerForm.startPoint.latitude" autocomplete="off" disabled />
        </el-form-item>
        <el-form-item label="站点类型" :label-width="formLabelWidth">
          <el-select v-model="routerForm.startPoint.pointType" placeholder="请选择站点类型">
            <el-option label="中心点" value="中心点" />
            <el-option label="外围点" value="外围点" />
            <el-option label="光缆点" value="光缆点" />
            <el-option label="定位点" value="single" />
          </el-select>
        </el-form-item>
        <el-form-item label="站点图标" :label-width="formLabelWidth">
          <el-upload ref="iconUpload" class="upload-demo" :action="uploadUrl"
            :on-success="handleIconSuccess('startPoint')" :on-error="handleUploadError"
            :before-upload="beforeIconUpload" :show-file-list="false" :headers="headers"
            :on-progress="handleUploadProgress" :http-request="customUpload">
            <el-button v-if="!routerForm.startPoint.iconUrl" size="mini">
              上传图标
            </el-button>
            <div v-if="routerForm.startPoint.iconUrl" class="upload-preview"
              style="position: relative; display: inline-block;">
              <el-image :src="routerForm.startPoint.iconUrl" alt="站点图标" class="thumbnail-image"
                :preview-src-list="[routerForm.startPoint.iconUrl]" @click.stop />
              <el-button size="mini" type="danger" circle icon="el-icon-delete" class="remove-btn"
                @click.stop="removeIcon('startPoint')"
                style="position: absolute; top: -10px; right: -10px; z-index: 10;" />
            </div>
            <div v-if="uploadingIcon" class="upload-progress">
              <el-progress :percentage="uploadIconProgress" status="active" />
            </div>
          </el-upload>
          <el-input v-model="routerForm.startPoint.iconUrl" autocomplete="off" style="display: none" />
        </el-form-item>

        <el-form-item label="站点图片" :label-width="formLabelWidth">
          <el-upload ref="imageUpload" class="upload-demo" :action="uploadUrl"
            :on-success="handleImageSuccess('startPoint')" :on-error="handleUploadError"
            :before-upload="beforeImageUpload" :show-file-list="false" :headers="headers"
            :on-progress="handleUploadProgress" :http-request="customUpload">
            <el-button v-if="!routerForm.startPoint.imgUrl" size="mini">
              上传图片
            </el-button>
            <div v-if="routerForm.startPoint.imgUrl" class="upload-preview"
              style="position: relative; display: inline-block;">
              <el-image :src="routerForm.startPoint.imgUrl" alt="站点图片" class="thumbnail-image"
                :preview-src-list="[routerForm.startPoint.imgUrl]" @click.stop />
              <el-button size="mini" type="danger" circle icon="el-icon-delete" class="remove-btn"
                @click.stop="removeImage('startPoint')"
                style="position: absolute; top: -10px; right: -10px; z-index: 10;" />
            </div>
            <div v-if="uploadingImage" class="upload-progress">
              <el-progress :percentage="uploadImageProgress" status="active" />
            </div>
          </el-upload>
          <el-input v-model="routerForm.startPoint.imgUrl" autocomplete="off" style="display: none" />
        </el-form-item>
        <el-form-item label="保障团队" :label-width="formLabelWidth">
          <el-upload v-if="!routerForm.startPoint.personUrl" class="upload-demo" :action="uploadUrl"
            :on-success="handleFileSuccess('startPoint')" :on-error="handleFileError" :show-file-list="false"
            :headers="headers" :on-progress="handleUploadProgress" :http-request="customUpload" multiple :limit="3"
            style="width: 20%; float: left;">
            <el-button size="mini">
              上传文件
            </el-button>
          </el-upload>
          <el-link v-if="!routerForm.startPoint.personUrl" type="primary"
            @click="downloadTemplate('保障团队上传模板.xlsx', 'http://**********:9000/important-protection-test/template/%E4%BF%9D%E9%9A%9C%E5%9B%A2%E9%98%9F%E4%B8%8A%E4%BC%A0%E6%A8%A1%E6%9D%BF.xlsx')">
            下载模板
          </el-link>
          <!-- <el-link v-if="!routerForm.startPoint.personUrl" href="http://**********:9000/important-protection-test/2025/07/01/02dd76047a2741a3a3454131729ff950.xlsx" type="primary">下载模板</el-link> -->
          <el-input v-else v-model="routerForm.startPoint.personUrl" clearable autocomplete="off" />
        </el-form-item>
        <el-form-item label="资源" :label-width="formLabelWidth">
          <el-upload v-if="!routerForm.startPoint.resourceUrl" class="upload-demo" :action="uploadUrl"
            :on-success="handleResourceFileSuccess('startPoint')" :on-error="handleFileError" :show-file-list="false"
            :headers="headers" :on-progress="handleUploadProgress" :http-request="customUpload" multiple :limit="3"
            style="width: 20%; float: left;">
            <el-button size="mini">
              上传文件
            </el-button>
          </el-upload>
          <el-link v-if="!routerForm.startPoint.resourceUrl" type="primary"
            @click="downloadTemplate('资源数据上传模板.xlsx', 'http://**********:9000/important-protection-test/template/%E8%B5%84%E6%BA%90%E6%95%B0%E6%8D%AE%E4%B8%8A%E4%BC%A0%E6%A8%A1%E6%9D%BF.xlsx')">
            下载模板
          </el-link>
          <el-input v-else v-model="routerForm.startPoint.resourceUrl" clearable autocomplete="off" />
        </el-form-item>
      </el-form>
      <el-form v-if="activeName == 'endPoint'" :model="routerForm.endPoint" size="mini">
        <el-form-item label="站点别名" :label-width="formLabelWidth">
          <el-input v-model="routerForm.endPoint.pointAlias" autocomplete="off" />
        </el-form-item>
        <el-form-item label="站点位置" :label-width="formLabelWidth">
          <el-input v-model="routerForm.endPoint.pointName" autocomplete="off" disabled />
        </el-form-item>
        <el-form-item label="站点精度" :label-width="formLabelWidth">
          <el-input v-model="routerForm.endPoint.longitude" autocomplete="off" disabled />
        </el-form-item>
        <el-form-item label="站点维度" :label-width="formLabelWidth">
          <el-input v-model="routerForm.endPoint.latitude" autocomplete="off" disabled />
        </el-form-item>

        <el-form-item label="站点类型" :label-width="formLabelWidth">
          <el-select v-model="routerForm.endPoint.pointType" placeholder="请选择站点类型">
            <el-option label="中心点" value="中心点" />
            <el-option label="外围点" value="外围点" />
            <el-option label="光缆点" value="光缆点" />
            <el-option label="定位点" value="single" />
          </el-select>
        </el-form-item>
        <el-form-item label="站点图标" :label-width="formLabelWidth">
          <el-upload ref="iconUpload" class="upload-demo" :action="uploadUrl"
            :on-success="handleIconSuccess('endPoint')" :on-error="handleUploadError" :before-upload="beforeIconUpload"
            :show-file-list="false" :headers="headers" :on-progress="handleUploadProgress" :http-request="customUpload">
            <el-button v-if="!routerForm.endPoint.iconUrl" size="mini">
              上传图标
            </el-button>
            <div v-if="routerForm.endPoint.iconUrl" class="upload-preview"
              style="position: relative; display: inline-block;">
              <el-image :src="routerForm.endPoint.iconUrl" alt="站点图标" class="thumbnail-image"
                :preview-src-list="[routerForm.endPoint.iconUrl]" @click.stop />
              <el-button size="mini" type="danger" circle icon="el-icon-delete" class="remove-btn"
                @click.stop="removeIcon('endPoint')"
                style="position: absolute; top: -10px; right: -10px; z-index: 10;" />
            </div>
            <div v-if="uploadingIcon" class="upload-progress">
              <el-progress :percentage="uploadIconProgress" status="active" />
            </div>
          </el-upload>
          <el-input v-model="routerForm.endPoint.iconUrl" autocomplete="off" style="display: none" />
        </el-form-item>
        <el-form-item label="站点图片" :label-width="formLabelWidth">
          <el-upload ref="imageUpload" class="upload-demo" :action="uploadUrl"
            :on-success="handleImageSuccess('endPoint')" :on-error="handleUploadError"
            :before-upload="beforeImageUpload" :show-file-list="false" :headers="headers"
            :on-progress="handleUploadProgress" :http-request="customUpload">
            <el-button v-if="!routerForm.endPoint.imgUrl" size="mini">
              上传图片
            </el-button>
            <div v-if="routerForm.endPoint.imgUrl" class="upload-preview"
              style="position: relative; display: inline-block;">
              <el-image :src="routerForm.endPoint.imgUrl" alt="站点图片" class="thumbnail-image"
                :preview-src-list="[routerForm.endPoint.imgUrl]" @click.stop />
              <el-button size="mini" type="danger" circle icon="el-icon-delete" class="remove-btn"
                @click.stop="removeImage('endPoint')"
                style="position: absolute; top: -10px; right: -10px; z-index: 10;" />
            </div>
            <div v-if="uploadingImage" class="upload-progress">
              <el-progress :percentage="uploadImageProgress" status="active" />
            </div>
          </el-upload>
          <el-input v-model="routerForm.endPoint.imgUrl" autocomplete="off" style="display: none" />
        </el-form-item>
        <el-form-item label="保障团队" :label-width="formLabelWidth">
          <el-upload v-if="!routerForm.endPoint.personUrl" class="upload-demo" :action="uploadUrl"
            :on-success="handleFileSuccess('endPoint')" :on-error="handleFileError" :show-file-list="false"
            :headers="headers" :on-progress="handleUploadProgress" :http-request="customUpload" multiple :limit="3"
            style="width: 20%; float: left;">
            <el-button size="mini">
              上传文件
            </el-button>
          </el-upload>
          <el-link v-if="!routerForm.endPoint.personUrl" type="primary"
            @click="downloadTemplate('保障团队上传模板.xlsx', 'http://**********:9000/important-protection-test/template/%E4%BF%9D%E9%9A%9C%E5%9B%A2%E9%98%9F%E4%B8%8A%E4%BC%A0%E6%A8%A1%E6%9D%BF.xlsx')">
            下载模板
          </el-link>
          <el-input v-else v-model="routerForm.endPoint.personUrl" clearable autocomplete="off" />
        </el-form-item>
        <el-form-item label="资源" :label-width="formLabelWidth">
          <el-upload v-if="!routerForm.endPoint.resourceUrl" class="upload-demo" :action="uploadUrl"
            :on-success="handleResourceFileSuccess('endPoint')" :on-error="handleFileError" :show-file-list="false"
            :headers="headers" :on-progress="handleUploadProgress" :http-request="customUpload" multiple :limit="3"
            style="width: 20%; float: left;">
            <el-button size="mini">
              上传文件
            </el-button>
          </el-upload>
          <el-link v-if="!routerForm.endPoint.resourceUrl" type="primary"
            @click="downloadTemplate('资源数据上传模板.xlsx', 'http://**********:9000/important-protection-test/template/%E8%B5%84%E6%BA%90%E6%95%B0%E6%8D%AE%E4%B8%8A%E4%BC%A0%E6%A8%A1%E6%9D%BF.xlsx')">
            下载模板
          </el-link>
          <el-input v-else v-model="routerForm.endPoint.resourceUrl" clearable autocomplete="off" />
        </el-form-item>
        <!-- <el-form-item
          label="防火墙数量"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="routerForm.endPoint.fhqNum"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          label="交换机数量"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="routerForm.endPoint.jhjNum"
            autocomplete="off"
          />
        </el-form-item> -->
      </el-form>
      <div v-if="activeName == 'line'">
        <el-form :model="routerForm" size="mini">
          <el-form-item label="线路名称" :label-width="formLabelWidth">
            <el-input v-model="routerForm.lineName" autocomplete="off" />
          </el-form-item>
          <el-form-item label="连线颜色" :label-width="formLabelWidth">
            <el-color-picker v-model="routerForm.lineColor" />
          </el-form-item>
          <el-form-item label="连线类型" :label-width="formLabelWidth">
            <el-select v-model="routerForm.lineType" placeholder="连线类型">
              <el-option label="直线" value="info" />
              <el-option label="虚线" value="dashed" />
              <el-option label="告警" value="alarm" />
            </el-select>
          </el-form-item>
          <el-form-item label="带宽" :label-width="formLabelWidth">
            <el-input v-model="routerForm.bandWidth" placeholder="带宽" autocomplete="off" />
          </el-form-item>
          <el-form-item label="专线号" :label-width="formLabelWidth">
            <el-input v-model="routerForm.zxNum" placeholder="专线号" autocomplete="off" />
          </el-form-item>
          <el-form-item label="关联电路" :label-width="formLabelWidth">
            <el-select v-model="routerForm.circuitId" filterable placeholder="电路名称"
              @change="handleCircuitChange(routerForm.circuitId)">
              <el-option v-for="item in latitudeData" :key="item.circuitId" :label="item.circuitName"
                :value="item.circuitId" />
            </el-select>
            <!-- <el-input
              v-model="form.latitude"
              autocomplete="off"
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
              />
            </el-input> -->
          </el-form-item>
        </el-form>
        <p>已添加 {{ circuitTabs.length }} 个电路到视图</p>
        <el-tabs v-model="activeTab" type="card" closable @tab-remove="removeTab">
          <el-tab-pane v-for="tab in circuitTabs" :key="tab.circuitId" :name="tab.circuitId">
            <template #label>
              <el-tooltip :content="tab.circuitName" placement="top">
                <span class="tab-label">{{ tab.circuitName }}</span>
              </el-tooltip>
            </template>

            <div style="margin: 10px 0;">
              路由信息
              <el-link type="primary" @click="goRouterEdit(tab.circuitId)">
                编辑电路
              </el-link>
            </div>

            <el-table :data="tab.routerData" stripe height="300" style="width: 100%;">
              <el-table-column prop="atrsNeName" label="A端设备名称" />
              <el-table-column prop="aportName" label="A端端口" />
              <el-table-column prop="ztrsNeName" label="Z端设备名称" />
              <el-table-column prop="zportName" label="Z端端口" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSiteChanges">取 消</el-button>
        <el-button type="primary" @click="confirmSiteChanges">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 线路选点设置 -->
    <el-dialog 
      class="setupDialog" 
      title="设置点属性" 
      :visible.sync="setupDialog" 
      width="50vw" 
      :modal="false"
      closable
    >
      <el-form 
        :model="routerForm.startPoint"
        size="mini"
      >
        <el-form-item
          label="站点别名"
          :label-width="formLabelWidth"
        >
          <el-input 
            v-model="routerForm.startPoint.pointAlias"
            autocomplete="off"
          />
        </el-form-item>
        <!-- <el-form-item 
          label="站点类型" 
          :label-width="formLabelWidth"
        >
          <el-select 
            v-model="routerForm.startPoint.pointType" 
            placeholder="请选择站点类型"
          >
            <el-option label="中心点" value="中心点" />
            <el-option label="外围点" value="外围点" />
            <el-option label="光缆点" value="光缆点" />
            <el-option label="定位点" value="single" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="专业类型" :label-width="formLabelWidth">
          <div style="display: flex; gap: 10px;">
            <el-input v-model="form.pointType" @click="openTypeDialog" placeholder="点击选择专业类型" readonly style="flex: 1;"></el-input>
            <el-button @click="testDialog" type="primary" size="mini">选择</el-button>
          </div>
        </el-form-item>
        <!-- <el-form-item label="设备端口" :label-width="formLabelWidth">
          
        </el-form-item> -->
        <el-form-item label="站点图标" :label-width="formLabelWidth">
          <el-upload 
            ref="iconUpload" 
            class="upload-demo" 
            :action="uploadUrl"
            :on-success="handleIconSuccess('startPoint')" 
            :on-error="handleUploadError"
            :before-upload="beforeIconUpload" 
            :show-file-list="false" 
            :headers="headers"
            :on-progress="handleUploadProgress" 
            :http-request="customUpload">
            <el-button 
              v-if="!routerForm.startPoint.iconUrl" 
              size="mini"
            >
              上传图标
            </el-button>
            <div v-if="routerForm.startPoint.iconUrl" class="upload-preview"
              style="position: relative; display: inline-block;">
              <el-image :src="routerForm.startPoint.iconUrl" alt="站点图标" class="thumbnail-image"
                :preview-src-list="[routerForm.startPoint.iconUrl]" @click.stop />
              <el-button size="mini" type="danger" circle icon="el-icon-delete" class="remove-btn"
                @click.stop="removeIcon('startPoint')"
                style="position: absolute; top: -10px; right: -10px; z-index: 10;" />
            </div>
            <div v-if="uploadingIcon" class="upload-progress">
              <el-progress :percentage="uploadIconProgress" status="active" />
            </div>
          </el-upload>
          <el-input v-model="routerForm.startPoint.iconUrl" autocomplete="off" style="display: none" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="setupDialog = false;">取 消</el-button>
        <el-button type="primary" @click="confirmAddPositionDot">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 类型选择弹出框 -->
    <el-dialog title="选择类型" :visible.sync="showTypeDialog" width="800px" :close-on-click-modal="false" :z-index="3000">
      <div class="type-selection-container">
        <!-- 级联选择区域 -->
        <div class="selection-area">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="selection-item">
                <label>所属专业:</label>
                <el-select 
                  v-model="selectedProfession" 
                  clearable
                  placeholder="请选择专业" 
                  @change="onProfessionChange" 
                >
                  <el-option
                    v-for="item in professionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="selection-item">
                <label>设备类型:</label>
                <el-select 
                  v-model="selectedDeviceType" 
                  :disabled="!selectedProfession"
                  placeholder="请选择设备类型" 
                  clearable
                  @change="onDeviceTypeChange" 
                >
                  <el-option
                    v-for="item in deviceTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 表格区域 -->
        <div
          v-if="profession==='space'"
          class="table-area"
          style="margin-top: 20px;"
        >
          <el-table
            :data="cascadeTableData"
            border
            style="width: 100%"
            max-height="300"
          >
            <el-table-column
              prop="roomNo"
              label="roomNo"
              show-overflow-tooltip
            />
            <el-table-column
              prop="chinaName"
              label="chinaName"
              show-overflow-tooltip
            />
            <el-table-column
              label="操作"
              width="120"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="selectTableItem(scope.row)"
                >
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页器 -->
          <el-pagination
            class="pagination"
            background
            layout="total, prev, pager, next"
            :total="tableDataTotal"
            :page-size="tableDataPageSize"
            :current-page="tableDataPageNum"
            @current-change="handleTableDataCurrentChange"
          />
        </div>
        <div
          v-if="profession==='device'"
          class="table-area"
          style="margin-top: 20px;"
        >
          <el-table
            :data="deviceTableData"
            border
            style="width: 100%"
            max-height="300"
            @selection-change="selectTableItem"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
            />
            <el-table-column
              prop="eqpNo"
              label="eqpNo"
              show-overflow-tooltip
            />
            <el-table-column
              prop="eqpName"
              label="eqpName"
              show-overflow-tooltip
            />
            <el-table-column
              label="操作"
              width="120"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="checkBorderCard(scope.row)"
                >
                  查看板卡
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页器 -->
          <el-pagination
            class="pagination"
            background
            layout="total, prev, pager, next"
            :total="deviceTableDataTotal"
            :page-size="deviceTableDataPageSize"
            :current-page="deviceTableDataPageNum"
            @current-change="handleDeviceTableDataCurrentChange"
          />
        </div>
        <div
          v-if="profession==='device' && cardVisible"
          class="table-area"
          style="margin-top: 20px;"
        >
          <el-table
            :data="cardTableData"
            border
            style="width: 100%"
            max-height="300"
            @selection-change="selectTableItem"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
            />
            <el-table-column
              prop="cardNo"
              label="cardNo"
              show-overflow-tooltip
            />
            <el-table-column
              prop="cardName"
              label="cardName"
              show-overflow-tooltip
            />
            <el-table-column
              label="操作"
              width="120"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="checkPort(scope.row)"
                >
                  查看端口
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页器 -->
          <el-pagination
            class="pagination"
            background
            layout="total, prev, pager, next"
            :total="cardTableDataTotal"
            :page-size="cardTableDataPageSize"
            :current-page="cardTableDataPageNum"
            @current-change="handleCardTableDataCurrentChange"
          />
        </div>
      </div>
      <div
        v-if="profession==='device' && portVisible"
        class="table-area"
        style="margin-top: 20px;"
      >
        <el-table
          :data="portTableData"
          border
          style="width: 100%"
          max-height="300"
        >
          <el-table-column
            prop="portNo"
            label="portNo"
            show-overflow-tooltip
          />
          <el-table-column
            prop="portName"
            label="portName"
            show-overflow-tooltip
          />
          <el-table-column
            label="操作"
            width="120"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                @click="selectTableItem(scope.row)"
              >
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页器 -->
        <el-pagination
          class="pagination"
          background
          layout="total, prev, pager, next"
          :total="portDataTotal"
          :page-size="portTableDataPageSize"
          :current-page="portDataPageNum"
          @current-change="handlePortTableDataCurrentChange"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showTypeDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmTypeSelection">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 地图选点提示 -->
    <div v-if="isSelectingRoute" class="route-selector-tip">
      <span>{{ routeSelectionStep === 1 ? '请选择线路起始点' : '请选择线路终止点' }}</span>
      <el-button size="mini" type="danger" style="margin-left: 10px;" @click="cancelSelectingRoute">
        取消
      </el-button>
    </div>

    <!-- 地图容器 -->
    <div id="allmap" class="map-container" />
    <!-- 拖拽组件配置 -->
    <div
      v-if="showTail"
      class="container"
      style="position: fixed;
      top: 55%;
      left: 53%;
      height: 85%;
      width: 25%;
      z-index: 2;"
    >
      <!-- 可拖拽容器 -->
      <div v-show="editMode && setUpType == '组件'" class="mapLeftTop2" style="margin-bottom: 20px;">
        <div class="widget-header">
          <span class="mapLeftTopTitle">组件设置</span>
        </div>
        <div class="back_table">
          <el-table :data="draggableItems" stripe height="340" style="width: 95%;margin: 5px auto;">
            <el-table-column prop="modelName" label="模块名称" />
            <el-table-column prop="visibleState" label="是否显示">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.visibleState" style="display: block" active-color="#ccc"
                  inactive-color="#13ce66" active-value="1" inactive-value="0" />
              </template>
            </el-table-column>
            <el-table-column prop="title" label="显示名称">
              <template slot-scope="scope">
                {{ scope.row.modelConfig.title }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 拖拽组件 -->
    <!-- v-show="editMode && setUpType == '组件'" -->
    <div style="position: fixed;top: 0; z-index: 2;">
      <div class="mapLeftTopz">
        <div v-show="showTail" class="right" style="z-index:5;cursor:pointer;" @click="closeShowTail" />
        <div v-show="!showTail" class="left" style="z-index:5;cursor:pointer;" @click="closeShowTail" />
      </div>
      <div v-if="showTail" class="container" style="position: fixed;
        top: 13%;
        left: 1%;
        height: 85%;
        width: 25%;">
        <!-- 可拖拽容器 -->
        <draggable v-model="leftItems" :group="{ name: 'dashboard', pull: 'clone', put: true }"
          :options="{ animation: 150 }" class="draggable-area leftItem" :move="onMove" @start="handleDragStart"
          @end="handleDragEnd">
          <!-- 动态渲染各区块组件 -->
          <div v-for="item in leftItems" :key="item.modelConfig.id">
            <div v-if="item.visibleState == 0" class="dashboard-widget" :class="item.modelConfig.className"
              style="margin-bottom: 20px;">
              <div class="widget-header">
                <el-input v-if="editMode" v-model="item.modelConfig.title" class="bg-black" />
                <span v-else class="mapLeftTopTitle">{{ item.modelConfig.title }}</span>
                <!-- <div
                v-if="item.actions"
                class="widget-actions"
              >
                <span
                  v-for="action in item.actions"
                  :key="action.id"
                  @click="action.handler()" 
                >
                  <i :class="action.icon" />
                </span>
              </div> -->
              </div>

              <!-- 动态组件渲染 -->
              <component :is="item.modelConfig.component" :data="getWidgetData(item.modelConfig.id)"
                :config="item.modelConfig" class="widget-content" @customEvent="customEvent" />
            </div>
          </div>
        </draggable>
      </div>
      <div v-if="showTail" class="container" style="position: fixed;
        right: -1%;
        top: 13%;
        height: 85%;
        width: 25%;">
        <!-- 可拖拽容器 -->
        <draggable v-model="rightItems" :group="{ name: 'dashboard', pull: 'clone', put: true }"
          :options="{ animation: 150 }" class="draggable-area rightItem" :move="onMove" @start="handleDragStart"
          @end="handleDragEnd">
          <!-- 动态渲染各区块组件 -->
          <div v-for="item in rightItems" :key="item.modelConfig.id">
            <div v-if="item.visibleState == '0'" class="dashboard-widget" :class="item.modelConfig.className"
              style="margin-bottom: 20px;">
              <div class="widget-header">
                <el-input v-if="editMode" v-model="item.modelConfig.title" class="bg-black" />
                <span v-else class="mapLeftTopTitle">{{ item.modelConfig.title }}</span>
                <!-- <div
                v-if="item.actions"
                class="widget-actions"
              >
                <span
                  v-for="action in item.actions"
                  :key="action.id"
                  @click="action.handler()" 
                >
                  <i :class="action.icon" />
                </span>
              </div> -->
              </div>

              <!-- 动态组件渲染 -->
              <component :is="item.modelConfig.component" :data="getWidgetData(item.modelConfig.id)"
                :config="item.modelConfig" class="widget-content" @customEvent="customEvent" />
            </div>
          </div>
        </draggable>
      </div>
    </div>
    <div v-if="alarmPopoverShow" class="alarmPopover">
      <div class="alarmPopoverTitle">
        告警详情
      </div>
      <span class="alarmPopoverX" @click="alarmPopoverQuit" />
      <div class="alarmPopoverText4 alarm">
        <el-table :data="tableData" stripe style="width: 100%;" default-expand-all max-height="380"
          :cell-style="{ color: '#ffffff', top: '5px' }" :row-class-name="tableRowClassName"
          :header-cell-style="{ background: 'transparent', top: '5px' }">
          <el-table-column prop="circuitStart" :width="80" label="起点" />
          <el-table-column prop="circuitEnd" :width="80" label="终点" />
          <el-table-column prop="circuitNo" label="电路代号" />
          <el-table-column prop="locateNeName" label="告警位置" />
          <el-table-column prop="alarmTitle" label="告警名称" />
          <el-table-column prop="alarmCreateTime" label="告警发生时间" />
          <el-table-column prop="workOrderNumberJt" label="工单号" />
        </el-table>
      </div>
    </div>
  </div>
</template>


<script>
import { formatTime } from "../assets/js/index.js";
import darkStyle from '../assets/js/darkStyle.json';
import $ from "jquery";
import utils from "./mixins.js";
import draggable from 'vuedraggable';
import vClickOutside from 'v-click-outside'
// import BMapGL from 'bmapgl';

export default {
  name: "ReinsuranceBigScreen",
  directives: {
    clickOutside: vClickOutside.directive
  },
  components: {
    draggable
  },
  mixins: [utils],
  data() {
    return {
      alarmPopoverShow: false,
      blockConfig2: [],
      blockConfig: [],
      // 编辑状态管理
      isPointSelected: false,
      addRouter: false,
      editMode: true,
      setupPointDialog: false,
      setupDialog:false,
      configEditing: {
        title: "陕西重保OSS监控系统",
        mapCenter: [108.953418, 34.274803],
        mapZoom: 13
      },

      // 基础数据
      showTail: true,
      currentTime: "",

      // 场景控制
      scene: 1,

      // 地图相关
      view: null,
      map: null,
      mapLoaded: false,
      rotateTimer: null,
      rotateAngle: 0,

      // 基础配置
      basicConf: {
        title: "陕西重保OSS监控系统",
        mapConfig: {
          center: [108.953418, 34.274803],
          zoom: 13
        },
        autoRotate: {
          enable: true,
          interval: 30000
        }
      },

      // 线路选点相关
      pointsType: '',
      isSelectingRoute: false,
      routeSelectionStep: 0, // 0: 未开始, 1: 选择起始点, 2: 选择终止点
      routeStartPoint: null,
      routeEndPoint: null,
      routeMarkers: [], // 存储选点标记
      isNamingRoute: false,
      newRouteName: '',


      // 搜索功能相关
      searchKeyword: '',
      searchResults: [],
      searchPopupVisible: false,
      isSearching: false,
      searchError: null,

      showDropdown: '', // 控制哪个下拉框显示，'startPoint'或'endPoint'
      addressList: [], // 存储搜索到的地址列表

      // 地图服务相关（需引入百度地图API）
      localSearch: null,
      geocoder: null,

      // 线路绘制相关
      isDrawingRoute: false,
      currentRoute: null,
      routePolyline: null, // 临时线路
      savedRoutes: [], // 保存的线路数据
      drawingManager: {},
      // 线路相关
      routerForm: {
        "startPoint": {
          "pointName": "",
          "pointAlias": "",
          "longitude": "",
          "latitude": "",
          "iconUrl": "",
          "imgUrl": "",
          "pointType": "",
        },
        "endPoint": {
          "pointName": "",
          "pointAlias": "",
          "longitude": "",
          "latitude": "",
          "iconUrl": "",
          "imgUrl": "",
          "pointType": "",
        },
        "lineName": "",
        "businessTypeList": "5",
        "lineColor": "#FFF387",
        "lineType": "info",
        "businessType": '',
        'founder': 'admin',
        'circuitId': '',
        "bandWidth": '',
        "zxNum": ''

      },
      routes: [], // 存储所有已创建的线路
      tableData: [],
      form: {
        pointName: '',
        coordinates: '',
        pointType: ''
      },
      formLabelWidth: '100px',
      uploadUrl: '/protect-api/api/file/upload',
      headers: {
        Authorization: `Bearer ${this.$store.state.token || ''}`
      },
      // 上传状态
      uploadingIcon: false,
      uploadingImage: false,
      uploadIconProgress: 0,
      uploadImageProgress: 0,
      // 服务器基础URL
      serverBaseUrl: '/api',
      currentMarker: null,

      // 站点/线路/组件
      setUpType: "站点",
      activeName: 'startPoint',
      screenList: [],
      screenDataList: [],
      draggableItems: [], // 可拖拽组件列表（动态排序）
      leftItems: [],   // 左侧画布组件
      rightItems: [],  // 右侧画布组件

      visible: false, // 是否展示右键菜单
      top: 0,
      left: 0,
      rightClickItem: {},
      selectedPolyline: null,
      originalStart: {},
      originalEnd: {},
      isLockingEndpoints: false,
      posDataId: '',
      lineDataId: '',
      latitudeData: [],
      uniqueId: '1',
      sceneList: {},
      nowChangeLineData: {},
      currentMenuId: null,
      startItem: '',
      startMarker: null, // 存储起点标记点
      endMarker: null,   // 存储终点标记点
      startMarker1: null, // 存储起点标记点
      endMarker1: null,   // 存储终点标记点
      routeLine: null,   // 新增：存储线路覆盖物（用于清除旧线路）

      activeTab: '',
      circuitTabs: [], // 存储所有打开的电路Tab
      addedMarkers:[],

      // 类型选择弹出框相关
      showTypeDialog: false,
      selectedProfession: '', // 所属专业
      selectedDeviceType: '', // 设备类型
      selectedBoard: '', // 板卡
      selectedPort: '', // 端口

      // 级联选择数据
      professionOptions: [
        { value: 'space', label: '空间资源' },
        { value: 'device', label: '设备资源' }
      ],
      deviceTypeOptions: [],

      // 表格数据
      cascadeTableData: [],
      tableDataTotal: 0,
      tableDataPageSize: 10,
      tableDataPageNum: 1,
      deviceTableData: [],
      deviceTableDataTotal: 0,
      deviceTableDataPageSize: 10,
      deviceTableDataPageNum: 1,
      profession: '',
      cardVisible: false,
      cardTableData: [],
      cardTableDataTotal: 0,
      cardTableDataPageSize: 10,
      cardTableDataPageNum: 1,
      portVisible: false,
      portTableData: [],
      portDataTotal: 0,
      portTableDataPageSize: 10,
      portDataPageNum: 1,
      cardRow: {},
      portRow: {},
    };
  },

  computed: {
    // 筛选后的线路列表
    filteredRoutes() {
      let result = [...this.routes];

      // 应用搜索筛选
      if (this.routeSearchKeyword) {
        const keyword = this.routeSearchKeyword.toLowerCase();
        result = result.filter(route =>
          route.name.toLowerCase().includes(keyword) ||
          route.startPoint[0].toString().includes(keyword) ||
          route.startPoint[1].toString().includes(keyword) ||
          route.endPoint[0].toString().includes(keyword) ||
          route.endPoint[1].toString().includes(keyword)
        );
      }

      // 应用排序
      if (this.sortField) {
        result.sort((a, b) => {
          if (this.sortField === 'name') {
            return this.sortOrder === 'ascending'
              ? a.name.localeCompare(b.name)
              : b.name.localeCompare(a.name);
          } else if (this.sortField === 'length') {
            return this.sortOrder === 'ascending'
              ? parseFloat(a.length) - parseFloat(b.length)
              : parseFloat(b.length) - parseFloat(a.length);
          }
          return 0;
        });
      }

      // 应用分页
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return result.slice(startIndex, endIndex);
    }
  },
  watch: {
    // 监听场景变化，重新加载图层
    // scene() {
    //   this.map.clearOverlays();
    //   this.addMapLayers();
    // },

    // 监听routes变化，重新绘制线路
    routes: {
      deep: true,
      handler() {
        // 当routes变化时，重新绘制所有线路
        if (this.map && this.mapLoaded) {
          this.map.clearOverlays();
          this.addMapLayers();
        }
      }
    },
    // 监听 visible，来触发关闭右键菜单，调用关闭菜单的方法
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu);
      } else {
        document.body.removeEventListener('click', this.closeMenu);
      }
    },
    draggableItems: {
      handler(newItems, oldItems) {
        // 过滤掉无效项（无id或title的项）
        // // console.log(newItems);
        const validItems = newItems.filter(item => item && item.modelConfig.id && item.modelConfig.title);

        if (validItems.length !== newItems.length) {
          console.warn('过滤无效布局项，已修正');
          this.draggableItems = validItems;
          return;
        }

        // // console.log('布局已更新:', validItems.map(item => item.modelConfig.title));
        this.saveLayoutToStorage();
      },
      deep: true,
      immediate: true
    },
    // 监听起点输入框
    'routerForm.startPoint.pointName'(newVal) {
      if (!newVal) { // 输入框为空时
        this.clearStartPoint(); // 清除起点相关数据和标记
      }
    },
    // 监听终点输入框
    'routerForm.endPoint.pointName'(newVal) {
      if (!newVal) { // 输入框为空时
        console.log(newVal);

        this.clearEndPoint(); // 清除终点相关数据和标记
      }
    }
  },
  created() {
    let screenList = JSON.parse(this.$route.query.screenList);
    screenList.map(item => {
      if (item.screenType == 'line') {
        this.lineDataId = item.screenId;
      } else if (item.screenType == 'pos') {
        this.posDataId = item.screenId;
      }
    });
    this.scene = '1940319540861431809'
    let sceneList = JSON.parse(this.$route.query.sceneList);
    this.sceneList = sceneList;
    this.routerForm.founder = sceneList.creater;
    if (sceneList.publishedStatus == 1) {
      this.editMode = false;
      this.showTail = true;
    } else {
      this.editMode = true;
      this.showTail = false;
    }
    this.configEditing.title = sceneList.sceneName;
    this.configEditing = { ...this.configEditing, ...sceneList };
    this.basicConf.title = sceneList.sceneName;
  },
  mounted() {
    this.getAllScreen();
    // 初始化地图
    this.initMap();
    // 启动自动旋转
    // this.startAutoRotate();

    // 更新时间
    this.updateCurrentTime();
    setInterval(() => this.updateCurrentTime(), 1000);

    // 编辑模式下添加地图监听
    if (this.editMode) {
      this.map.addEventListener('dragend', this.handleMapDrag);
      this.map.addEventListener('zoomend', this.handleMapZoom);
    }

    // 添加绘图完成事件监听
    this.drawingManager.addEventListener('overlaycomplete', (e) => {
      // e.overlay 是绘制完成的覆盖物对象
      const overlay = e.overlay;

      // 处理不同类型的覆盖物
      if (overlay.getPath) {
        // 线或多边形有getPath方法
        const path = overlay.getPath();
        // console.log('绘制完成，路径点数量:', path.length);

        // 这里可以根据需要处理路径数据
        // 例如：保存线路、计算长度等
      } else if (overlay.getCenter) {
        // 圆有getCenter方法
        const center = overlay.getCenter();
        const radius = overlay.getRadius();
        // console.log('绘制完成，圆心:', center, '半径:', radius);
      } else if (overlay.getPosition) {
        // 标记点有getPosition方法
        const position = overlay.getPosition();
        // console.log('绘制完成，标记点位置:', position);
      }

      // 绘制完成后关闭绘图管理器
      this.drawingManager.close();

      // 重置绘图按钮样式
      const drawingButtons = document.querySelectorAll('.bmap-btn');
      drawingButtons.forEach(btn => {
        btn.style.backgroundPositionY = '0';
      });
    });

  },
  beforeDestroy() {
    // 清除定时器
    clearInterval(this.rotateTimer);

    // 移除编辑模式下的事件监听
    if (this.editMode) {
      this.map.removeEventListener('dragend', this.handleMapDrag);
      this.map.removeEventListener('zoomend', this.handleMapZoom);
    }

    // 移除选点事件监听
    this.map.removeEventListener('click', this.handleMapClick);
  },
  methods: {
    handleChange(value) {
      console.log(value);
    },
    // 确认新增位置点
    confirmAddPositionDot(){
      
    },
    customEvent(msg) {
      // console.log(msg);
      if (!this.editMode) {
        this.sTDataClick(msg.message);
      }
    },
    // 初始化地图
    initMap() {
      this.map = new BMapGL.Map('allmap');
      const center = new BMapGL.Point(
        this.configEditing.mapCenter[0],
        this.configEditing.mapCenter[1]
      );
      this.map.centerAndZoom(center, this.configEditing.mapZoom);
      this.map.enableScrollWheelZoom(true);
      this.map.setTilt(45);
      this.changeMapStyle();

      // 监听地图加载完成
      this.map.addEventListener('tilesloaded', () => {
        this.mapLoaded = true;
        this.getAllLines(this.setUpType == '站点' ? this.posDataId : this.lineDataId);
        this.addMapLayers();
        // this.addReguaranteeLogic();  //增加重保逻辑
      });


      // 初始化搜索服务
      this.initSearchServices();
      this.initContextMenu();
      var styleOptions = {
        strokeColor: '#5E87DB',   // 边线颜色
        fillColor: '#5E87DB',     // 填充颜色。当参数为空时，圆形没有填充颜色
        strokeWeight: 2,          // 边线宽度，以像素为单位
        strokeOpacity: 1,         // 边线透明度，取值范围0-1
        fillOpacity: 0.2          // 填充透明度，取值范围0-1
      };
      var labelOptions = {
        borderRadius: '2px',
        background: '#FFFBCC',
        border: '1px solid #E1E1E1',
        color: '#703A04',
        fontSize: '12px',
        letterSpacing: '0',
        padding: '5px'
      };
      // 实例化鼠标绘制工具
      this.drawingManager = new BMapGLLib.DrawingManager(this.map, {
        enableCalculate: false, // 绘制是否进行测距测面
        enableSorption: true,   // 是否开启边界吸附功能
        sorptiondistance: 20,   // 边界吸附距离
        circleOptions: styleOptions,     // 圆的样式
        polylineOptions: styleOptions,   // 线的样式
        polygonOptions: styleOptions,    // 多边形的样式
        rectangleOptions: styleOptions,  // 矩形的样式
        labelOptions: labelOptions,      // label样式
      });


    },

    // addReguaranteeLogic() {
    //   let _this = this;
    //   let queryData = { "busType": this.posDataId };
    //   _this.$axios.get(`/protect-api/pointLine/getAllPointLine/${this.posDataId}`).then((data) => {
    //     if (data.data.code === "0000") {
    //       _this.reguaranteeLogicList = data.data.data.zbPosList;
    //       _this.reguaranteeLogicList2 = data.data.data.zbLines;
    //       _this.markList = _this.reguaranteeLogicList;

    //       // 创建位置ID到位置对象的映射，方便快速查找
    //       const posMap = {};
    //       _this.reguaranteeLogicList.forEach(pos => {
    //         posMap[pos.posId] = pos;
    //       });

    //       // 处理线段数据
    //       _this.reguaranteeLogicList2.forEach(line => {
    //         const sourcePos = posMap[line.posIdSource];
    //         const targetPos = posMap[line.posIdTarget];

    //         if (sourcePos && targetPos) {
    //           // 创建线段
    //           let polylineArr = [];

    //           if (line.lineStr) {
    //             const points = line.lineStr.split(';');
    //             points.forEach(pointStr => {
    //               const [lng, lat] = pointStr.split(',');
    //               polylineArr.push(new BMapGL.Point(parseFloat(lng), parseFloat(lat)));
    //             });
    //           }
    //           // 设置线段样式
    //           let color = ["#dfde3a", "#dfde3a", "#dfde3a"];
    //           let border = 2;

    //           if (line.lineColor) {
    //             color = line.lineColor.split(",");
    //           }

    //           if (line.lineBold) {
    //             border = line.lineBold;
    //           }

    //           // 创建并添加线段
    //           let polyline = new BMapGL.Polyline(polylineArr, {
    //             strokeColor: color[0],
    //             strokeWeight: border,
    //             strokeOpacity: 0.9
    //           });

    //           _this.map.addOverlay(polyline);

    //           // 为线段添加右键事件（编辑模式下）
    //           if (this.editMode && this.scene === 4) {
    //             polyline.addEventListener('rightclick', function (e) {
    //               _this.selectedPolyline = this;
    //               _this.handlePolylineRightClick(e);
    //             });
    //           }

    //           // 处理告警点
    //           if (line.lineType === "event") {
    //             // 添加警告图标
    //             if (line.lineStr) {
    //               const points = line.lineStr.split(';');
    //               if (points.length >= 2) {
    //                 const p1 = points[0].split(',');
    //                 const p2 = points[1].split(',');
    //                 const pointArr = [
    //                   new BMapGL.Point(Number(p1[0]), Number(p1[1])),
    //                   new BMapGL.Point(Number(p2[0]), Number(p2[1]))
    //                 ];

    //                 const middenP = _this.getMidpoiont(pointArr);
    //                 const urlP = require("../assets/zxx/warning.png");
    //                 const myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(25, 25));
    //                 const marker = new BMapGL.Marker(middenP, { icon: myIcon });
    //                 _this.map.addOverlay(marker);
    //               }
    //             }
    //           }

    //           if (line.lineType === "alarm") {
    //             // 添加错误图标
    //             if (line.lineStr) {
    //               const points = line.lineStr.split(';');
    //               if (points.length >= 2) {
    //                 const p1 = points[0].split(',');
    //                 const p2 = points[1].split(',');
    //                 const pointArr = [
    //                   new BMapGL.Point(Number(p1[0]), Number(p1[1])),
    //                   new BMapGL.Point(Number(p2[0]), Number(p2[1]))
    //                 ];

    //                 const middenP = _this.getMidpoiont(pointArr);
    //                 const urlP = require("../assets/zxx/quit.png");
    //                 const myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(16, 16));
    //                 const marker = new BMapGL.Marker(middenP, { icon: myIcon });
    //                 _this.map.addOverlay(marker);
    //               }
    //             }
    //           }
    //         }
    //       });

    //       // 创建标记点
    //       _this.reguaranteeLogicList.forEach((item, index) => {
    //         let point = new BMapGL.Point(item.posLng, item.posLat);
    //         let myIcon = new BMapGL.Icon(item.posUrl || "http://10.93.15.92/images/zb/zhd.png", new BMapGL.Size(23, 33));
    //         let marker = new BMapGL.Marker(point, { icon: myIcon });

    //         marker.myData = item;
    //         marker.customData = item;
    //         _this.map.addOverlay(marker);

    //         // 设置标签
    //         let lableText = _this.initTabchuang(item);
    //         marker.setLabel(lableText);

    //         // 设置第一个点的动画
    //         // if (index === 0) {
    //         //   marker.setAnimation(BMAP_ANIMATION_BOUNCE);
    //         // }

    //         // 绑定鼠标事件
    //         marker.addEventListener("click", function (e) {

    //           // 清除上一个标记点（如果存在）
    //           if (this.startMarker) {
    //             this.map.removeOverlay(this.startMarker);
    //           }
    //           if (this.startMarker1) {
    //             this.map.removeOverlay(this.startMarker1);
    //           }
    //           if (this.endMarker) {
    //              this.map.removeOverlay(this.endMarker);
    //              this.endMarker = null; // 重置终点标记
    //            }
    //            if (this.endMarker1) {
    //              this.map.removeOverlay(this.endMarker1);
    //              this.endMarker1 = null; // 重置终点标记
    //            }
    //           _this.isPointSelected = true;

    //           // 路由规划功能
    //           if (_this.addRouter) {
    //             if (_this.routeSelectionStep === 1) {
    //               // 设置起点坐标
    //               _this.routerForm.startPoint.longitude = e.srcElement.myData.posLng;
    //               _this.routerForm.startPoint.latitude = e.srcElement.myData.posLat;
    //               _this.routerForm.startPoint.pointName = e.srcElement.myData.posName;
    //               _this.routerForm.startPoint.pointAlias = e.srcElement.myData.posName;
    //               _this.routerForm.startPoint.iconUrl = e.srcElement.myData.posImgUrlXianLu;
    //               _this.routerForm.startPoint.imgUrl = e.srcElement.myData.posRealUrl;
    //               _this.routerForm.lineName = _this.routerForm.startPoint.pointAlias + '/' + _this.routerForm.endPoint.pointAlias;
    //               if (_this.routerForm.endPoint.longitude && _this.routerForm.endPoint.latitude) {
    //                 _this.connectStartAndEndPoints();
    //               }
    //             } else if (_this.routeSelectionStep === 2) {
    //               // 设置终点坐标
    //               _this.routerForm.endPoint.longitude = e.srcElement.myData.posLng;
    //               _this.routerForm.endPoint.latitude = e.srcElement.myData.posLat;
    //               _this.routerForm.endPoint.pointName = e.srcElement.myData.posName;
    //               _this.routerForm.endPoint.pointAlias = e.srcElement.myData.posName;
    //               _this.routerForm.endPoint.iconUrl = e.srcElement.myData.posImgUrlXianLu;
    //               _this.routerForm.endPoint.imgUrl = e.srcElement.myData.posRealUrl;
    //               _this.routerForm.lineName = _this.routerForm.startPoint.pointAlias + '/' + _this.routerForm.endPoint.pointAlias;
    //               if (_this.routerForm.startPoint.longitude && _this.routerForm.startPoint.latitude) {
    //                 _this.connectStartAndEndPoints();
    //               }
    //             }
    //           }

    //           // 点击信息展示控制
    //           if (_this.clickInfomation) {
    //             marker.removeEventListener("mouseout", function () { });
    //             _this.clickInfomation = false;
    //           } else {
    //             marker.addEventListener("mouseout", function () {
    //               // _this.queryData.posId = "";
    //               // _this.getSTData(_this.queryData);
    //               // _this.getYjData(_this.queryData);
    //               let list2Length2 = _this.reguaranteeLogicList.length;
    //               if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
    //                 document.getElementsByClassName("BMapLabel")[list2Length2].remove();
    //               }
    //             });
    //             _this.clickInfomation = true;
    //           }
    //         });
    //       });

    //       // 延迟处理标签样式
    //       setTimeout(() => {
    //         for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
    //           if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
    //             document.getElementsByClassName("BMapLabel")[i].style.border = "1px dashed #1ef1a5";
    //             document.getElementsByClassName("BMapLabel")[i].style.padding = "";
    //           }
    //         }
    //         // $(".BMapLabel").hide();
    //       }, 250);
    //     }
    //   });
    // },
    addReguaranteeLogic() {
      let _this = this;
      let queryData = { "busType": this.posDataId };

      // 1. 定义不同lineType对应的样式配置
      const lineTypeConfig = {
        info: {
          color: ["#FFF387", "#FFF387", "#FFF387"], // 默认浅黄色
          weight: 1,
          lineStyle: 'info', // 实线
          animation: false
        },
        dashed: {
          color: ["#FF9800", "#FF9800", "#FF9800"], // 橙色
          weight: 2,
          lineStyle: 'dashed', // 虚线
          animation: false
        },
        alarm: {
          color: ["#F44336", "#F44336", "#F44336"], // 红色
          weight: 3,
          lineStyle: 'solid', // 实线
          animation: true // 开启闪烁动画
        },
        // 可扩展其他类型...
        default: {
          color: ["#DFDE3A", "#DFDE3A", "#DFDE3A"], // 默认黄色
          weight: 2,
          lineStyle: 'solid',
          animation: false
        }
      };

      _this.$axios.get(`/protect-api/pointLine/getAllPointLine/${this.posDataId}`).then((data) => {
        if (data.data.code === "0000") {
          // 注意：接口返回的是zbLineStrList和zbPos，而非原代码的zbPosList和zbLines
          _this.reguaranteeLogicList = data.data.data.zbPosList; // 点数据
          _this.reguaranteeLogicList2 = data.data.data.zbLines; // 线数据
          _this.markList = _this.reguaranteeLogicList;

          // 创建位置ID到位置对象的映射，方便快速查找
          const posMap = {};
          _this.reguaranteeLogicList.forEach(pos => {
            posMap[pos.posId] = pos;
          });

          // 处理线段数据
          _this.reguaranteeLogicList2.forEach(line => {
            console.log(line);

            // 2. 根据lineType获取配置（默认使用default）
            const config = lineTypeConfig[line.lineType] || lineTypeConfig.default;

            // 创建线段点数组（优先使用lineArr，避免重复解析）
            let polylineArr = [];
            if (line.lineArr && line.lineArr.length) {
              // 直接使用接口返回的lineArr数组
              polylineArr = line.lineArr.map(point =>
                new BMapGL.Point(parseFloat(point[0]), parseFloat(point[1]))
              );
            } else if (line.lineStr) {
              // 兼容原逻辑：从lineStrBD09解析（百度坐标系）
              const points = line.lineStr.split(';');
              points.forEach(pointStr => {
                const [lng, lat] = pointStr.split(',');
                polylineArr.push(new BMapGL.Point(parseFloat(lng), parseFloat(lat)));
              });
            }
            console.log(line);

            if (polylineArr.length < 2) return; // 至少需要2个点才能形成线

            // 3. 合并配置与接口返回的样式（接口返回优先）
            let color = [...config.color];
            let border = config.weight;
            let lineStyle = config.lineStyle;

            // 接口返回的颜色优先
            if (line.lineColor) {
              color = line.lineColor.split(',').length ? line.lineColor.split(',') : [line.lineColor];
            }
            // 接口返回的线宽优先
            if (line.lineBold) {
              border = parseInt(line.lineBold, 10);
            }

            // 4. 创建线段
            let polyline = new BMapGL.Polyline(polylineArr, {
              strokeColor: color[0],
              strokeWeight: border,
              strokeOpacity: 0.9,
              strokeStyle: lineStyle, // 实线/虚线
              strokeDasharray: lineStyle === 'dashed' ? [10, 5] : [] // 虚线样式
            });
            console.log(polyline);
            line.originalPolyline=polyline
            _this.map.addOverlay(polyline);

            // 5. 为告警线添加闪烁动画
            if (config.animation) {
              _this.addLineAnimation(polyline);
            }

            if (this.editMode && this.scene === 4) {
              // 为线段添加右键事件（编辑模式下）
              polyline.addEventListener('rightclick', function (e) {
                _this.selectedPolyline = this;
                _this.handlePolylineRightClick(e);
              });
            }
            if (this.setUpType === '站点') {
              // 1. 计算线段的中点坐标（保持您原有的polylineArr逻辑）
              const midPoint = _this.getMidpoiont(polylineArr);

              // 2. 仅当 circuitNum > 1 时处理
              if (line.circuitNum > 1) {
                // 3. 创建加号图标（完全保持您的原有代码）
                const iconUrl = require("../assets/zxx/Add.png");
                const plusIcon = new BMapGL.Icon(iconUrl, new BMapGL.Size(20, 20));
                const plusMarker = new BMapGL.Marker(midPoint, { icon: plusIcon });
                _this.map.addOverlay(plusMarker);

                // 4. 为当前线段添加临时状态（不修改原始line对象）
                const lineState = {
                  expanded: false,
                  expandedPolylines: [],
                  currentMarker: plusMarker
                };

                // 5. 点击事件处理
                plusMarker.addEventListener('click', (e) => {
                  if (lineState.expanded) {
                    // 收缩逻辑：删除展开的线段，显示加号
                    lineState.expandedPolylines.forEach(p => _this.map.removeOverlay(p));
                    lineState.expandedPolylines = [];
                    _this.map.addOverlay(line.originalPolyline);
                    plusMarker.setIcon(new BMapGL.Icon(
                      require("../assets/zxx/Add.png"),
                      new BMapGL.Size(20, 20)
                    ));
                  } else {
                    // 原始线段点集
                    const offsetPoints = polylineArr.map(p => new BMapGL.Point(p.lng, p.lat));
                    const startPoint = offsetPoints[0];
                    const endPoint = offsetPoints[1];

                    // 计算基础参数
                    const midLng = (startPoint.lng + endPoint.lng) / 2;
                    const midLat = (startPoint.lat + endPoint.lat) / 2;
                    const distance = Math.sqrt(
                      Math.pow(endPoint.lng - startPoint.lng, 2) +
                      Math.pow(endPoint.lat - startPoint.lat, 2)
                    );

                    // 为每条线路生成不同弯曲度的曲线
                    for (let i = 0; i < line.circuitNum; i++) {
                      // 计算当前线路的弯曲参数
                      const ratio = (i + 1) / line.circuitNum; // 弯曲度比例 (0-1]
                      const offset = distance * 0.1 * ratio;   // 弯曲程度随线路递增

                      // 计算控制点（不同弯曲方向）
                      const angle = Math.atan2(
                        endPoint.lat - startPoint.lat,
                        endPoint.lng - startPoint.lng
                      );
                      const controlAngle = angle + Math.PI / 4 * (i % 2 === 0 ? 1 : -1); // 交替方向

                      const controlLng = midLng + offset * Math.cos(controlAngle);
                      const controlLat = midLat + offset * Math.sin(controlAngle);
                      const controlPoint = new BMapGL.Point(controlLng, controlLat);

                      // 生成贝塞尔曲线点集
                      const curvePoints = [];
                      for (let t = 0; t <= 1; t += 0.02) {
                        const x = Math.pow(1 - t, 2) * startPoint.lng + 2 * t * (1 - t) * controlPoint.lng + Math.pow(t, 2) * endPoint.lng;
                        const y = Math.pow(1 - t, 2) * startPoint.lat + 2 * t * (1 - t) * controlPoint.lat + Math.pow(t, 2) * endPoint.lat;
                        curvePoints.push(new BMapGL.Point(x, y));
                      }

                      // 创建曲线（每条线不同颜色）
                      const polyline = new BMapGL.Polyline(curvePoints, {
                        strokeColor: ['#FF0000', '#00FF00', '#0000FF', '#FF00FF'][i % 4],
                        strokeWeight: 2, // 线宽递增
                        strokeOpacity: 0.8
                      });
                      _this.map.removeOverlay(line.originalPolyline);
                      _this.map.addOverlay(polyline);
                      lineState.expandedPolylines.push(polyline);
                    }

                    plusMarker.setIcon(new BMapGL.Icon(
                      require("../assets/zxx/jian.png"),
                      new BMapGL.Size(20, 20)
                    ));
                  }

                  lineState.expanded = !lineState.expanded;
                  _this.selectedPolyline = polyline; 
                  // console.log('当前线路:', line);
                  // console.log('当前线路起终点:', polylineArr);
                });
              }
            }

            // 处理线段中间的告警图标（保持原逻辑）
            if (line.lineType === "event" || line.lineType === "alarm") {
              const middenP = _this.getMidpoiont(polylineArr); // 获取线段中点
              const iconUrl = line.lineType === "event"
                ? require("../assets/zxx/warning.png")
                : require("../assets/zxx/quit.png");
              const iconSize = line.lineType === "event" ? new BMapGL.Size(25, 25) : new BMapGL.Size(16, 16);

              const myIcon = new BMapGL.Icon(iconUrl, iconSize);
              const marker = new BMapGL.Marker(middenP, { icon: myIcon });
              _this.map.addOverlay(marker);
            }
          });

          // 创建标记点（保持原逻辑，略作调整）
          _this.reguaranteeLogicList.forEach((item) => {
            let point = new BMapGL.Point(parseFloat(item.posLng), parseFloat(item.posLat));
            // 点图标优先使用接口返回的posUrl
            let iconUrl = item.posUrl || "http://10.93.15.92/images/zb/zhd.png";
            let myIcon = new BMapGL.Icon(iconUrl, new BMapGL.Size(23, 33));
            let marker = new BMapGL.Marker(point, { icon: myIcon });

            marker.myData = item;
            marker.customData = item;
            _this.map.addOverlay(marker);

            // 设置标签
            let lableText = _this.initTabchuang(item);
            marker.setLabel(lableText);

            // 绑定点击事件（保持原逻辑）
            marker.addEventListener("click", function (e) {
              // ...（原点击事件逻辑不变）
            });
          });

          // 延迟处理标签样式
          setTimeout(() => {
            const labels = document.getElementsByClassName("BMapLabel");
            _this.reguaranteeLogicList.forEach((_, i) => {
              if (labels[i]) {
                labels[i].style.border = "1px dashed #1ef1a5";
                labels[i].style.padding = "0";
              }
            });
          }, 250);
        }
      });
    },
    handleLineClick(e) { console.log(e) },
    // 新增：为线段添加闪烁动画（针对告警线）
    addLineAnimation(polyline) {
      let isVisible = true;
      setInterval(() => {
        isVisible = !isVisible;
        polyline.setStrokeOpacity(isVisible ? 0.9 : 0.3); // 透明度闪烁
      }, 800);
    },
    getYjData(params) {
      let _this = this;
      _this.$axios.post("/protect-api/zb/getYjwz", params).then((data) => {
        if (data.data.code === '0000') {
          // // console.log(data);
          this.yjData = data.data.data;
        }
      });
    },
    // getSTData(queryData) {
    //   let _this = this;
    //   _this.$axios.post("/protect-api/zb/getSTData", queryData).then((data) => {
    //     if (data.data.code === '0000') {
    //       _this.sTDataInfo = data.data.data;
    //     }
    //   });
    // },
    initTabchuang(param) {
      let url = param.posRealUrl;
      let bottom2 = require("../assets/zxx/bottom2.png");
      let locationTab = require("../assets/zxx/locationTab.png");
      let text = '<div style="z-index: 1; width:170Px;height: 112Px;background-image: url(' + locationTab + ') ;background-repeat: no-repeat;background-size: 100% 100%;">' +
        '<div style="z-index: 1;display: flex;' +
        'flex-direction: row;align-content: center;justify-content: space-between; align-items: center;">' +
        '            <img src="' + url + ' " id="imgDemo" alt="" style="height:90px;width:100%">' +
        '    </div>' +
        '<div style="width: 100%;height: 18px;font-size: 14px;text-align:center;font-family: Source Han Sans CN;font-weight: 400;color: #FFFFFF;line-height: 22px;">' + param.alias + '</div>' +
        '</div>' +
        '<div style="position: relative;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
      var label = new BMapGL.Label(text, { offset: new BMapGL.Size(-91, -150) });
      return label;
    },
    async tabchuang(param) {
      let _this = this;
      let bottom = require("../assets/zxx/bottom.png");
      let explains = require("../assets/zxx/explains.png");
      let query = {};
      query.dataType = "1";
      if (param.posLevel == 'X') {
        if (param.posName === "联通省公司") {
          query.userGroupType = "GROUP_SF";
          query.posId = "";
        } else if (param.posName === "联通西安分公司") {
          query.userGroupType = "GROUP_XF";
          query.posId = "";
        }
      } else {
        query.posId = param.posId;
        query.userGroupType = "GROUP_XC";
      }
      /*      let query={};*/
      await _this.$axios.post("/protect-api/person/getAllPersonList", query).then((data) => {
        if (data.data.code === "0000") {
          _this.zbPosiNFO = data.data.data;
          /*          param.target.customInfo = _this.zbPosiNFO;*/
        } else {
          _this.zbPosiNFO = null;
        }
      });
      let text1 = '<div id="information" style="z-index: 1003;width: 365Px;height: 265Px;border: 1Px solid;border-image: linear-gradient(0deg, #4AB38C, #105745) 10 10;background: linear-gradient(0deg, #0E2D28 0%, rgba(7,70,83,0.8) 100%);">' +
        '    <div>' +
        '        <div style="z-index:3;width: 365Px;height: 39Px;background: linear-gradient(90deg, #25BFAB 0%, rgba(25,146,156,0.5) 100%);opacity: 0.3;">' +
        '        </div>' +
        '        <span style="z-index:3;margin-top:-38px;margin-left:10px;position:fixed;width: 78Px;opacity: 1;height: 18Px;font-size: 18Px;font-family: Source Han Sans CN;' +
        '        font-weight: 500;color: #FFFFFF;line-height: 38Px;text-shadow: 0Px 2Px 8Px rgba(5,28,55,0.42);">重保团队</span>' +
        '        <span id="informationExplains" style="position: fixed;margin-top: -33px;margin-left: 85px;"><img src="' + explains + '" style="width: 16px;height:16px;"></span>' +
        '        <span id="informationExplainsDetails" style="position: fixed;z-index:4;margin-top: -70px;margin-left: 80px;' +
        'background: #07607c;position: absolute;border-radius: 4px;padding: 10px;font-size: 12px;line-height: 1.2;min-width: 10px;word-wrap: break-word;' +
        ' color: #FFF;display: none">注：再次点击图标隐藏此弹窗（点击定位图标）</span>' +
        '        <div id="openZhongbaoTeam" style="margin-top:-32px;margin-left:313px;position:fixed;width: 50px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 500;color: #FFFFFF;line-height: 29px;cursor:pointer;">详情 ></div>' +
        '    </div>' +
        '    <div class="contont" style="z-index:1003; position:relative; display: inline-block;width: 360px;height: 217px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #FFFEFE;line-height: 28px; overflow-y:auto;overflow-x:hidden;">';
      let text3 = '    </div>' +
        '</div>' +
        '<div style="position: relative;margin-top: -1px;"> <img src="' + bottom + ' "  alt="" style="position: absolute;left: 45%;"></div>';
      let text2 = "";
      if (undefined != _this.zbPosiNFO && null != _this.zbPosiNFO && _this.zbPosiNFO.length > 0) {
        _this.zbPosiNFO.map(item => {
          text2 = text2 + ' <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">' +
            '            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>' +
            '            <span style="display: inline-block;margin-left:3px;width: 60px;height: 25px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;' +
            '  ">' + item.userName + '</span>' +
            '            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">电话：</span>' +
            '            <span style="margin-left:-2px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">' + item.userPhone + '</span>' +
            '            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">专业：</span>' +
            '            <span style="width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">' + item.userProfession + '</span>' +
            '        </div>';
        });
      } else {
        text2 = '        <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">' +
          '            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>' +
          '            <span style="margin-left:3px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">暂无信息</span>' +
          '        </div>';
      }
      let label = new BMapGL.Label(text1 + text2 + text3, { offset: new BMapGL.Size(-177, -301) });
      return label;
    },

    // 修改后的draw方法
    draw(drawingType) {
      this.addRouter = true;
      // 获取所有绘图按钮
      const drawingButtons = document.querySelectorAll('.bmap-btn');

      // 重置所有按钮样式
      drawingButtons.forEach(btn => {
        btn.style.backgroundPositionY = '0';
      });

      // 获取当前点击的按钮
      const currentButton = document.getElementById(drawingType);
      if (currentButton) {
        // 设置当前按钮样式
        currentButton.style.backgroundPositionY = '-52px';
      }

      // 根据按钮ID确定绘图类型常量
      let drawingTypeConstant;
      switch (drawingType) {
        case 'marker':
          drawingTypeConstant = BMAP_DRAWING_MARKER;
          break;
        case 'polyline':
          drawingTypeConstant = BMAP_DRAWING_POLYLINE;
          break;
        case 'rectangle':
          drawingTypeConstant = BMAP_DRAWING_RECTANGLE;
          break;
        case 'polygon':
          drawingTypeConstant = BMAP_DRAWING_POLYGON;
          break;
        case 'circle':
          drawingTypeConstant = BMAP_DRAWING_CIRCLE;
          break;
        default:
          return;
      }

      // 控制绘图管理器
      if (this.drawingManager._isOpen && this.drawingManager.getDrawingMode() === drawingTypeConstant) {
        this.drawingManager.close();
        // 移除事件监听
        this.removeDrawingEvents();
      } else {
        this.drawingManager.setDrawingMode(drawingTypeConstant);
        this.drawingManager.open();
        // 添加事件监听
        this.addDrawingEvents(drawingType);
      }
    },
    openMapClicks(val) {
      this.routeSelectionStep = val;
      this.isPointSelected = false;
      // 清除对应位置的旧标记点和线路
      if (val === 1) {
        // 点击起点输入框：清除上一个起点标记
        if (this.startMarker) {
          this.map.removeOverlay(this.startMarker);
          this.startMarker = null; // 重置起点标记
        }
        if (this.startMarker1) {
          this.map.removeOverlay(this.startMarker1);
          this.startMarker1 = null; // 重置起点标记
        }
      } else if (val === 2) {
        // 点击终点输入框：清除上一个终点标记
        if (this.endMarker) {
          this.map.removeOverlay(this.endMarker);
          this.endMarker = null; // 重置终点标记
        }
        if (this.endMarker1) {
          this.map.removeOverlay(this.endMarker1);
          this.endMarker1 = null; // 重置终点标记
        }
      }

      // 清除旧线路（如果存在）
      if (this.routeLine) {
        this.map.removeOverlay(this.routeLine);
        this.routeLine = null;
      }
      // 重新绑定地图点击事件（先移除旧事件避免重复绑定）
      this.map.removeEventListener('click', this.handleMapClick);
      this.map.addEventListener('click', this.handleMapClick);
    },
    // 清除起点
    clearStartPoint() {
      // 1. 清除地图上的起点标记
      if (this.startMarker) {
        this.map.removeOverlay(this.startMarker);
        this.startMarker = null; // 重置标记对象
        this.map.removeEventListener('click', this.handleMapClick);
      }
      if (this.startMarker1) {
        this.map.removeOverlay(this.startMarker1);
        this.map.removeEventListener('click', this.handleMapClick);
      }
      // 2. 清空起点坐标数据
      this.routerForm.startPoint = {
        pointName: '',
        longitude: '',
        latitude: '',
        pointAlias: ''
      };
      // 3. 清除线路（如果起点清空，线路已无效）
      this.clearRouteLine();
      // 4. 更新线路名称（如果终点还存在）
      this.updateLineName();
    },

    // 清除终点
    clearEndPoint() {
      // 1. 清除地图上的终点标记
      let _this = this;
      if (this.endMarker) {
        _this.map.removeOverlay(_this.endMarker);
        _this.map.removeOverlay(_this.endMarker1);
        _this.endMarker = null; // 重置标记对象
        this.map.removeEventListener('click', this.handleMapClick);
      }
      if (this.endMarker1) {
        _this.map.removeOverlay(_this.endMarker1);
        _this.endMarker1 = null; // 重置标记对象
        this.map.removeEventListener('click', this.handleMapClick);
      }
      // 2. 清空终点坐标数据
      this.routerForm.endPoint = {
        pointName: '',
        longitude: '',
        latitude: '',
        pointAlias: ''
      };
      // 3. 清除线路（如果终点清空，线路已无效）
      this.clearRouteLine();
      // 4. 更新线路名称（如果起点还存在）
      this.updateLineName();
    },

    // 清除线路覆盖物
    clearRouteLine() {
      if (this.routeLine) {
        this.map.removeOverlay(this.routeLine);
        this.routeLine = null;
      }
    },

    // 更新线路名称（避免清空后名称残留）
    updateLineName() {
      const startName = this.routerForm.startPoint.pointName || '';
      const endName = this.routerForm.endPoint.pointName || '';
      this.routerForm.lineName = startName && endName ? `${startName} / ${endName}` : '';
    },
    // 添加绘图事件监听
    addDrawingEvents(drawingType) {
      // 清除之前的信息
      this.clearCoordinatesInfo();

      // 根据不同的绘图类型添加相应的事件监听
      switch (drawingType) {
        case 'marker':
          this.drawingManager.addEventListener('markercomplete', this.onMarkerComplete.bind(this));
          break;
        case 'polyline':
          this.drawingManager.addEventListener('polylinecomplete', this.onPolylineComplete.bind(this));
          this.drawingManager.addEventListener('lineupdate', this.onLineUpdate.bind(this));
          break;
        case 'rectangle':
          this.drawingManager.addEventListener('rectanglecomplete', this.onRectangleComplete.bind(this));
          break;
        case 'polygon':
          this.drawingManager.addEventListener('polygoncomplete', this.onPolygonComplete.bind(this));
          this.drawingManager.addEventListener('polygonupdate', this.onPolygonUpdate.bind(this));
          break;
        case 'circle':
          this.drawingManager.addEventListener('circlecomplete', this.onCircleComplete.bind(this));
          break;
      }
    },

    // 移除绘图事件监听
    removeDrawingEvents() {
      this.drawingManager.removeEventListener('markercomplete', this.onMarkerComplete.bind(this));
      this.drawingManager.removeEventListener('polylinecomplete', this.onPolylineComplete.bind(this));
      this.drawingManager.removeEventListener('lineupdate', this.onLineUpdate.bind(this));
      this.drawingManager.removeEventListener('rectanglecomplete', this.onRectangleComplete.bind(this));
      this.drawingManager.removeEventListener('polygoncomplete', this.onPolygonComplete.bind(this));
      this.drawingManager.removeEventListener('polygonupdate', this.onPolygonUpdate.bind(this));
      this.drawingManager.removeEventListener('circlecomplete', this.onCircleComplete.bind(this));
    },

    // 清除坐标信息显示
    clearCoordinatesInfo() {
      const infoDiv = document.getElementById('coordinates-info');
      if (infoDiv) {
        infoDiv.innerHTML = '';
      }
    },

    // 显示坐标信息
    // showCoordinatesInfo(info) {
    //   let infoDiv = document.getElementById('coordinates-info');
    //   if (!infoDiv) {
    //     infoDiv = document.createElement('div');
    //     infoDiv.id = 'coordinates-info';
    //     infoDiv.style.position = 'absolute';
    //     infoDiv.style.bottom = '10px';
    //     infoDiv.style.left = '10px';
    //     infoDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
    //     infoDiv.style.padding = '10px';
    //     infoDiv.style.borderRadius = '5px';
    //     infoDiv.style.zIndex = '100';
    //     document.body.appendChild(infoDiv);
    //   }
    //   infoDiv.innerHTML += `<p>${info}</p>`;
    // },

    // 标记点完成回调
    onMarkerComplete(marker) {
      const point = marker.getPosition();
      // this.showCoordinatesInfo(`标记点坐标: ${point.lng}, ${point.lat}`);
    },

    // 折线完成回调
    onPolylineComplete(polyline) {
      const path = polyline.getPath();
      console.log(path);
      const coordinates = path.map(point => `${point.lng}, ${point.lat}`).join('; ');
      // this.showCoordinatesInfo(`折线坐标: ${coordinates}`);

      // 获取每个点的地址信息
      this.getAddressesForPath(path);
    },

    // 获取路径上每个点的地址信息
    getAddressesForPath(path) {
      if (!this.geocoder) {
        this.geocoder = new BMapGL.Geocoder();
      }

      // 限制请求数量，避免超出API限制
      const MAX_POINTS = 10; // 最多获取10个点的地址信息
      const pointsToQuery = path.length <= MAX_POINTS ? path :
        path.filter((_, index) => index % Math.floor(path.length / MAX_POINTS) === 0 ||
          index === 0 || index === path.length - 1);

      // 显示加载状态
      // this.showCoordinatesInfo('正在获取地址信息...');

      // 串行处理地址请求，避免并发过多
      const processNextPoint = (index) => {
        console.log(index)
        if (index >= pointsToQuery.length) {
          return;
        }

        const point = pointsToQuery[index];
        this.$axios.post(`/protect-api/map/longitudeToAddress?lat=${point.lat.toFixed(8)}&lng=${point.lng.toFixed(8)}`,).then((data) => {
          if (data.data.code === "0000") {
            let result = data.data.data.result;
            if (index == 0) {
              this.routerForm.startPoint.pointName = result.formatted_address;
              this.routerForm.startPoint.longitude = point.lng.toFixed(8);
              this.routerForm.startPoint.latitude = point.lat.toFixed(8);
              this.routerForm.startPoint.pointAlias = result.formatted_address;
              processNextPoint(index + 1);
              // this.routerForm.startPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
            } else {
              this.routerForm.endPoint.pointName = result.formatted_address;
              this.routerForm.endPoint.longitude = point.lng.toFixed(8);
              this.routerForm.endPoint.latitude = point.lat.toFixed(8);
              this.routerForm.endPoint.pointAlias = result.formatted_address;
              // this.routerForm.endPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
            }
            this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
          }
        });
      };
      // this.routerForm.startPoint = result.address;
      // 开始处理第一个点
      processNextPoint(0);
    },

    // 显示坐标信息
    showCoordinatesInfo(info) {
      let infoDiv = document.getElementById('coordinates-info');
      if (!infoDiv) {
        infoDiv = document.createElement('div');
        infoDiv.id = 'coordinates-info';
        infoDiv.style.position = 'absolute';
        infoDiv.style.bottom = '10px';
        infoDiv.style.left = '10px';
        infoDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        infoDiv.style.padding = '10px';
        infoDiv.style.borderRadius = '5px';
        infoDiv.style.zIndex = '100';
        infoDiv.style.maxWidth = '400px';
        infoDiv.style.maxHeight = '200px';
        infoDiv.style.overflowY = 'auto';
        document.body.appendChild(infoDiv);
      }

      // 创建新的信息项
      const infoItem = document.createElement('p');
      infoItem.style.margin = '5px 0';
      infoItem.style.wordBreak = 'break-all';
      infoItem.innerHTML = info;
      infoDiv.appendChild(infoItem);

      // 自动滚动到底部
      infoDiv.scrollTop = infoDiv.scrollHeight;
    },


    // 折线更新回调（实时获取点位）
    onLineUpdate(polyline) {
      const path = polyline.getPath();
      if (path.length > 0) {
        const lastPoint = path[path.length - 1];
        // this.showCoordinatesInfo(`当前点位: ${lastPoint.lng}, ${lastPoint.lat}`);
      }
    },


    // 初始化搜索服务
    initSearchServices() {
      // 初始化本地搜索
      this.localSearch = new BMapGL.LocalSearch(this.map, {
        renderOptions: {
          map: this.map,
          autoViewport: true,
          panel: '' // 不使用默认面板，自定义结果展示
        },
        onSearchComplete: (results) => {
          this.handleSearchResults(results);
        }
      });

      // 初始化地理编码服务
      this.geocoder = new BMapGL.Geocoder();
    },


    // 搜索位置（带防抖）
    searchLocation(val, type) {
      // 清除之前的计时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      // 检查关键词
      const keyword = val.trim();
      if (!keyword) {
        this.searchError = '请输入搜索关键词';
        return;
      }

      // 设置防抖延迟
      this.debounceTimer = setTimeout(() => {
        // this.performSearch(keyword);
        this.searchPerform(keyword, type);
      }, 300);
    },
    async searchPerform(keyword, type) {
      await this.$axios.post(`/protect-api/map/AddressTolongitude?q=${keyword}&city=西安市&busType=${this.scene}&pageNum=1&pageSize=20`).then((data) => {
        if (data.data.code === "0000") {
          this.addressList = [];
          console.log(data.data.data);
          this.addressList = data.data.data;
          this.showDropdown = type; // 显示对应的下拉框
          const lng = parseFloat(data.data.UNI_BSS_BODY.SEARCH_POI_RSP.result.data.location.x);
          const lat = parseFloat(data.data.UNI_BSS_BODY.SEARCH_POI_RSP.result.data.location.y);
          const point = new BMapGL.Point(lng, lat);
          // console.log(point);

          // 添加标记
          const marker = new BMapGL.Marker(point);
          this.map.addOverlay(marker);

          // 添加信息窗口
          const infoWindow = new BMapGL.InfoWindow(`
            <div style="font-size: 12px;">
              <p>位置: ${keyword}</p>
              <p>坐标: ${point.lng}, ${point.lat}</p>
              <p>地址: ${keyword}</p>
            </div>
          `);
          // 自动打开信息窗口
          this.map.openInfoWindow(infoWindow, point);
          if (this.routeSelectionStep === 1) {
            this.routerForm.startPoint.pointName = keyword;
            this.routerForm.startPoint.longitude = point.lng.toFixed(8);
            this.routerForm.startPoint.latitude = point.lat.toFixed(8);
            this.routerForm.startPoint.pointAlias = keyword;
            this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
            if (this.routerForm.endPoint.longitude && this.routerForm.endPoint.latitude) {
              this.connectStartAndEndPoints();
            }
          } else {
            this.routerForm.endPoint.pointName = keyword;
            this.routerForm.endPoint.longitude = point.lng.toFixed(8);
            this.routerForm.endPoint.latitude = point.lat.toFixed(8);
            this.routerForm.endPoint.pointAlias = keyword;
            this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
            // 当终点选择完成后，连接起点和终点
            if (this.routerForm.startPoint.longitude && this.routerForm.startPoint.latitude) {
              this.connectStartAndEndPoints();
            }
            this.map.removeEventListener('click', this.handleMapClick);
          }
        }
      });
    },
    // 执行实际搜索
    performSearch(keyword) {
      this.isSearching = true;
      this.searchError = null;
      this.searchResults = [];

      // 检查是否是坐标格式
      const coordinateRegex = /^(\d+\.\d+),\s*(\d+\.\d+)$/;
      const match = keyword.match(coordinateRegex);

      if (match) {
        // 是坐标格式，直接定位
        try {
          const lng = parseFloat(match[1]);
          const lat = parseFloat(match[2]);
          const point = new BMapGL.Point(lng, lat);

          // this.map.centerAndZoom(point, 15);

          // 添加标记
          this.addMarker(point);

          // 获取地址信息
          this.geocoder.getLocation(point, (result) => {
            if (result) {
              this.searchResults = [{
                title: `坐标点 (${lng.toFixed(6)}, ${lat.toFixed(6)})`,
                address: result.address,
                point: point
              }];
            } else {
              this.searchResults = [{
                title: `坐标点 (${lng.toFixed(6)}, ${lat.toFixed(6)})`,
                address: '未知地址',
                point: point
              }];
            }

            this.isSearching = false;
          });
        } catch (error) {
          this.isSearching = false;
          this.searchError = '坐标格式不正确，请使用"经度,纬度"格式';
        }
      } else {
        // console.log('执行关键词搜索:', keyword);
        // 不是坐标格式，进行关键词搜索
        this.localSearch.search(keyword);
      }
    },

    // 处理搜索结果
    handleSearchResults(results) {
      this.isSearching = false;
      this.searchResults = [];

      // console.log('搜索结果对象:', results); // 添加调试信息

      // 检查结果是否有效
      if (!results) {
        this.searchError = '搜索结果无效';
        return;
      }
      // console.log(results.getPoi(0));

      // 检查搜索是否成功 - 使用百度地图API的错误码
      if (results) {
        // 检查结果数量
        const count = results.getCurrentNumPois ? results.getCurrentNumPois() : 0;

        if (count > 0) {
          for (let i = 0; i < count; i++) {
            const poi = results.getPoi(i);
            if (poi) {
              // 使用 push 方法添加结果，而不是覆盖
              this.searchResults.push({
                title: poi.title,
                address: poi.address || '未知地址',
                point: poi.point
              });
            }
          }

          if (this.searchResults.length === 1) {
            // 如果只有一个结果，直接选择
            this.selectSearchResult(this.searchResults[0]);
          } else {
            // 显示多个结果的弹窗
            this.searchPopupVisible = true;
          }
        } else {
          this.searchError = '未找到相关结果';
        }
      } else {
        // 搜索失败
        const errorMsg = results && results.error ?
          `搜索错误: ${results.error}` : '搜索失败，请重试';
        this.searchError = errorMsg;
        console.error('搜索错误:', errorMsg);
      }
    },


    // 选择搜索结果
    selectSearchResult(result) {
      this.selectedResult = result;
      this.searchPopupVisible = false;

      // 定位到选中的位置
      // this.map.centerAndZoom(result.point, 15);

      // 添加标记
      this.addMarker(result.point);

      // 隐藏搜索结果
      this.closeSearchResults();

      // 清空搜索框
      this.searchKeyword = '';
    },

    // 添加标记
    addMarker(point) {
      // 清除之前的标记
      // this.map.clearOverlays();

      // 添加新标记
      const marker = new BMapGL.Marker(point);
      this.map.addOverlay(marker);

      // 添加信息窗口
      const infoWindow = new BMapGL.InfoWindow(`
        <div style="font-size: 12px;">
          <p>位置: ${this.selectedResult ? this.selectedResult.title : '坐标点'}</p>
          <p>坐标: ${point.lng.toFixed(6)}, ${point.lat.toFixed(6)}</p>
          <p>地址: ${this.selectedResult ? this.selectedResult.address : '未知地址'}</p>
        </div>
      `);

      marker.addEventListener('click', () => {
        this.map.openInfoWindow(infoWindow, point);
      });

      // 自动打开信息窗口
      this.map.openInfoWindow(infoWindow, point);
    },

    // 关闭搜索结果
    closeSearchResults() {
      this.searchResults = [];
      this.searchError = null;
      this.isSearching = false;
    },

    // 清空搜索内容
    clearSearch() {
      this.searchKeyword = '';
    },
    // 处理地图点击事件
    handleMapClick(e) {
      if (!this.isPointSelected) {
        // this.map.clearOverlays();
        const point = e.latlng;
        const lng = point.lng.toFixed(8);
        const lat = point.lat.toFixed(8);
        // 清除上一个标记点（如果存在）
        if (this.startMarker) {
          this.map.removeOverlay(this.startMarker);
        }
        // 创建标记点
        const newMarker = new BMapGL.Marker(point, {
          icon: new BMapGL.Icon('http://**********:9000/important-protection-test/2025/06/13/0e8a696085dd49c7af68f6a0acb7a691.png', new BMapGL.Size(26, 42), {
            anchor: new BMapGL.Size(13, 42)
          })
        });

        // 添加标记点到地图
        this.map.addOverlay(newMarker);
        // 根据当前步骤（起点/终点）存储标记点
        if (this.routeSelectionStep === 1) {
          this.startMarker = newMarker; // 存入起点标记
        } else if (this.routeSelectionStep === 2) {
          this.endMarker = newMarker;   // 存入终点标记
        }

        // 获取点击点位的地址信息
        this.$axios.post(`/protect-api/map/longitudeToAddress?lat=${lat}&lng=${lng}`).then((data) => {
          if (data.data.code === "0000") {
            let result = data.data.data.UNI_BSS_BODY.REVERSE_GEOCODING_RSP.result;
            const address = (result.formatted_address || result.sematic_description);
            if (this.routeSelectionStep === 1) {
              // 显示信息窗口
              const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `, {
                offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
              });

              this.map.openInfoWindow(infoWindow, point);
              this.routerForm.startPoint.pointName = (result.formatted_address || result.sematic_description);
              this.routerForm.startPoint.longitude = point.lng.toFixed(8);
              this.routerForm.startPoint.latitude = point.lat.toFixed(8);
              this.routerForm.startPoint.pointAlias = (result.formatted_address || result.sematic_description);
              this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
              if (this.routerForm.endPoint.longitude && this.routerForm.endPoint.latitude) {
                this.connectStartAndEndPoints();
              }
              this.map.removeEventListener('click', this.handleMapClick);
              // this.routerForm.startPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
            } else if (this.routeSelectionStep === 2) {
              // 显示信息窗口
              const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `, {
                offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
              });
              this.map.openInfoWindow(infoWindow, point);
              this.routerForm.endPoint.pointName = (result.formatted_address || result.sematic_description);
              this.routerForm.endPoint.longitude = point.lng.toFixed(8);
              this.routerForm.endPoint.latitude = point.lat.toFixed(8);
              this.routerForm.endPoint.pointAlias = (result.formatted_address || result.sematic_description);
              this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
              // 当终点选择完成后，连接起点和终点
              if (this.routerForm.startPoint.longitude && this.routerForm.startPoint.latitude) {
                this.connectStartAndEndPoints();
              }
              this.map.removeEventListener('click', this.handleMapClick);
              // this.routerForm.endPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
            } else {
              // 未明确选择起点/终点时，提示用户
              this.$message({
                message: '请先点击"新增线路"按钮开始选择起点和终点',
                type: 'info'
              });
              // this.routerForm.lineName = this.routerForm.startPoint.pointName+' / '+this.routerForm.endPoint.pointName;
            };
          } else {
            let result = {
              "location": {
                "lng": lng,
                "lat": lat
              },
              "formatted_address": "陕西省西安市雁塔区等驾坡街道田马路",
              "edz": {
                "name": "曲江新区"
              },
              "business": "",
              "business_info": [],
              "addressComponent": {
                "country": "中国",
                "country_code": 0,
                "country_code_iso": "CHN",
                "country_code_iso2": "CN",
                "province": "陕西省",
                "city": "西安市",
                "city_level": 2,
                "district": "雁塔区",
                "town": "等驾坡街道",
                "town_code": "610113005",
                "distance": "",
                "direction": "",
                "adcode": "610113",
                "street": "田马路",
                "street_number": ""
              },
              "pois": [],
              "roads": [],
              "poiRegions": [],
              "sematic_description": "",
              "formatted_address_poi": "",
              "cityCode": 233
            };

            if (this.routeSelectionStep === 1) {
              const address = '起点';
              // 显示信息窗口
              const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `, {
                offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
              });
              this.map.openInfoWindow(infoWindow, point);
              this.routerForm.startPoint.pointName = address;
              this.routerForm.startPoint.longitude = point.lng.toFixed(8);
              this.routerForm.startPoint.latitude = point.lat.toFixed(8);
              this.routerForm.startPoint.pointAlias = address;
              this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
              if (this.routerForm.endPoint.longitude && this.routerForm.endPoint.latitude) {
                this.connectStartAndEndPoints();
              }
              // this.routerForm.startPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
            } else if (this.routeSelectionStep === 2) {
              // 显示信息窗口
              const address = '终点';
              const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `, {
                offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
              });
              this.map.openInfoWindow(infoWindow, point);
              this.routerForm.endPoint.pointName = address;
              this.routerForm.endPoint.longitude = point.lng.toFixed(8);
              this.routerForm.endPoint.latitude = point.lat.toFixed(8);
              this.routerForm.endPoint.pointAlias = address;
              this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
              // 当终点选择完成后，连接起点和终点
              if (this.routerForm.startPoint.longitude && this.routerForm.startPoint.latitude) {
                this.connectStartAndEndPoints();
              }
              this.map.removeEventListener('click', this.handleMapClick);
              // this.routerForm.endPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
            } else {
              // 未明确选择起点/终点时，提示用户
              this.$message({
                message: '请先点击"新增线路"按钮开始选择起点和终点',
                type: 'info'
              });
              // this.routerForm.lineName = this.routerForm.startPoint.pointName+' / '+this.routerForm.endPoint.pointName;
            };
          }
        });

      }

    },
    // 连接起点和终点的方法
    connectStartAndEndPoints() {
      // this.map.clearOverlays();
      if (this.currentMarker) {
        this.map.removeOverlay(this.currentMarker);
      }
      // 检查起点和终点是否都已选择
      if (this.routerForm.startPoint.longitude && this.routerForm.endPoint.longitude) {
        // 创建起点和终点的坐标点
        const startPoint = new BMapGL.Point(
          parseFloat(this.routerForm.startPoint.longitude),
          parseFloat(this.routerForm.startPoint.latitude)
        );

        const endPoint = new BMapGL.Point(
          parseFloat(this.routerForm.endPoint.longitude),
          parseFloat(this.routerForm.endPoint.latitude)
        );
        // console.log(startPoint,endPoint);

        let aUrl = 'http://**********:9000/important-protection-test/2025/06/13/0e8a696085dd49c7af68f6a0acb7a691.png';
        // 创建起点标记
        this.startMarker1 = new BMapGL.Marker(startPoint, {
          icon: new BMapGL.Icon(this.routerForm.startPoint.iconUrl ? this.routerForm.startPoint.iconUrl : aUrl,
            new BMapGL.Size(32, 32), {
            anchor: new BMapGL.Size(16, 32)
          })
        });

        // 创建终点标记
        this.endMarker1 = new BMapGL.Marker(endPoint, {
          icon: new BMapGL.Icon(this.routerForm.endPoint.iconUrl ? this.routerForm.endPoint.iconUrl : aUrl,
            new BMapGL.Size(32, 32), {
            anchor: new BMapGL.Size(16, 32)
          })
        });

        // 创建连接起点和终点的折线
        this.routeLine = new BMapGL.Polyline([startPoint, endPoint], {
          strokeColor: "#FF3300",  // 折线颜色
          strokeWeight: 3,         // 折线宽度
          strokeOpacity: 0.8,      // 折线透明度
          strokeStyle: "solid",    // 折线样式
          enableEditing: false     // 禁用编辑
        });

        // 添加折线和标记点到地图
        this.map.addOverlay(this.routeLine);
        this.map.addOverlay(this.startMarker1);
        this.map.addOverlay(this.endMarker1);

        // 计算并设置地图视图，确保起点和终点都在视野内
        // this.map.setViewport([startPoint, endPoint]);
        this.routeSelectionStep = 0;
      }
    },

    // 切换地图样式
    changeMapStyle() {
      this.map.setOptions({
        style: {
          styleJson: darkStyle
        },
        styleUrl: 'https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/mapstyle/mapstyle.json'
      });
    },

    // 添加地图图层
    addMapLayers() {
      this.map.clearOverlays();
      if (this.view) {
        this.view.removeAllLayers();
      }
      let _this = this;
      // 这里可以根据场景添加不同图层
      if (this.setUpType == '站点') {
        this.addReguaranteeLogic();
      } else if (this.setUpType === '组件') {
        this.getAllScreen();
      } else if (this.setUpType === '线路') {
        this.map.clearOverlays();

        _this.addLineMonitoring();  //增加线路监控
      }

      // 绘制所有已保存的线路
      // this.routes.forEach(route => {
      //   this.drawRoute(route);
      // });
    },

    // 添加场景1标记
    addScene1Markers() {
      // 模拟数据
      const markers = [
        { id: 1, lng: 108.9, lat: 34.3, iconUrl: 'https://example.com/server-icon.png' },
        { id: 2, lng: 109.0, lat: 34.2, iconUrl: 'https://example.com/network-icon.png' }
      ];

      markers.forEach(item => {
        const point = new BMapGL.Point(item.lng, item.lat);
        const icon = new BMapGL.Icon(item.iconUrl, new BMapGL.Size(32, 32), {
          anchor: new BMapGL.Size(16, 32)
        });

        const marker = new BMapGL.Marker(point, { icon });
        this.map.addOverlay(marker);

        // 添加点击事件
        marker.addEventListener('click', () => {
          this.showDeviceDetail(item.id);
        });
      });
    },

    // 添加场景2拓扑图
    addScene2Topology() {
      // 拓扑图逻辑...
    },

    // 切换场景
    changeScene(sceneId, type) {
      console.log(sceneId)
      this.showTail = type == '组件' ? true : false;
      this.scene = sceneId;
      this.setUpType = type;
      this.map.clearOverlays();
      this.addMapLayers();
    },
    changeSceneList(type) {
      if (!this.editMode) {
        this.map.clearOverlays();
        if (this.view) {
          this.view.removeAllLayers();
        }
        if (type.screenType == 'line') {
          this.addLineMonitoring();
        } else if (type.screenType == 'pos') {
          this.addReguaranteeLogic();
        }
      }

    },

    // 显示设备详情
    showDeviceDetail(deviceId) {
      // 模拟请求
      const device = {
        id: deviceId,
        name: `设备-${deviceId}`,
        type: '服务器',
        status: 'normal',
        ip: '192.168.1.' + deviceId,
        manager: '管理员',
        phone: '1380013800' + deviceId
      };

      this.showNoticePopover({
        title: "设备详情",
        content: `
          <div class="detail-item">设备名称: ${device.name}</div>
          <div class="detail-item">设备类型: ${device.type}</div>
          <div class="detail-item">当前状态: <span class="status-${device.status}">正常</span></div>
          <div class="detail-item">IP地址: ${device.ip}</div>
          <div class="detail-item">负责人: ${device.manager}</div>
          <div class="detail-item">联系电话: ${device.phone}</div>
        `
      });
    },

    // 显示弹窗
    showNoticePopover(obj) {
      this.noticePopoverObj = obj;
      this.noticePopoverShow = true;
    },

    // 关闭弹窗
    closeNoticePopover() {
      this.noticePopoverShow = false;
    },

    // 处理地图拖拽
    handleMapDrag() {
      const center = this.map.getCenter();
      this.configEditing.mapCenter = [center.lng, center.lat];
    },

    // 处理地图缩放
    handleMapZoom() {
      // this.configEditing.mapZoom = this.map.getZoom();
    },

    // 切换编辑模式
    toggleEditMode() {
      this.editMode = !this.editMode;

      if (this.editMode) {
        // 进入编辑模式
        this.map.addEventListener('dragend', this.handleMapDrag);
        this.map.addEventListener('zoomend', this.handleMapZoom);
      } else {
        // 退出编辑模式
        this.map.removeEventListener('dragend', this.handleMapDrag);
        this.map.removeEventListener('zoomend', this.handleMapZoom);
      }
    },

    // 取消编辑
    cancelEdit() {
      // this.configEditing = {
      //   title: this.basicConf.title,
      //   mapCenter: [...this.basicConf.mapConfig.center],
      //   mapZoom: this.basicConf.mapConfig.zoom
      // };
      this.getAllScreen();
      location.reload();
      // this.editMode = false;
    },

    // 保存配置
    saveConfig() {
      let params = {
        "sceneName": this.configEditing.title,
        "publishedStatus": 0,
        "creater": this.configEditing.creater,
        screens: this.screenDataList
      };
      // 保存到后端
      this.$axios.post(`/protect-api/scenes/update/${this.configEditing.id}`, params).then(res => {
        if (res.data.code === '0000') {
          this.$message({
            message: '配置保存成功，去管理页面发布！',
            type: 'success'
          });
          this.getAllScreen();
          // location.reload();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    convertVisibleStateToString(data) {
      // 处理数组类型
      if (Array.isArray(data)) {
        return data.map(item => this.convertVisibleStateToString(item));
      }

      // 处理对象类型
      if (typeof data === 'object' && data !== null) {
        const newData = { ...data }; // 创建新对象避免修改原数据

        // 遍历对象所有属性
        for (const key in newData) {
          if (newData.hasOwnProperty(key)) {
            // 找到 visibleState 字段，转换为字符串
            if (key === 'visibleState') {
              newData[key] = newData[key].toString();
            }
            // 递归处理子属性
            else {
              newData[key] = this.convertVisibleStateToString(newData[key]);
            }
          }
        }

        return newData;
      }

      // 基本数据类型直接返回
      return data;
    },
    async getAllScreen() {
      this.$axios.post(`/protect-api/scenes/getAllScreen?sceneId=${this.configEditing.id}`).then(res => {
        if (res.data.code === '0000') {
          this.screenDataList = res.data.data;
          this.screenDataList = this.convertVisibleStateToString(this.screenDataList);
          this.blockConfig = this.screenDataList[0].models;
          this.currentMenuId = this.screenDataList[0].orderNo;
          this.uniqueId = this.screenDataList[0].orderNo;
          if (this.screenDataList[0].screenType == 'pos') {
            this.getZJData('busType', this.screenDataList[0].screenId);
          } else if (this.screenDataList[0].screenType == 'line') {
            this.getZJData('sceneId', this.configEditing.id);
          }
          this.$nextTick(val => {
            this.loadLayoutFromStorage();
          });
        }
      });
    },
    getZJData(type, id) {
      this.getSTData(type, id);
      this.getYjwzData(type, id);
      this.getZbPerson(type, id);
      this.getGjNumData(type, id);
      this.getZxNumData(type, id);
      this.getMessageData();
    },

    // 修改：加载布局时使用菜单ID作为键
    loadLayoutFromStorage() {
      try {
        // 无保存布局时使用默认分割
        this.leftItems = this.blockConfig.slice(0, 3).map(item => ({ ...item }));
        this.rightItems = this.blockConfig.slice(3, 6).map(item => ({ ...item }));
        this.saveLayoutToStorage(); // 保存默认布局
        this.draggableItems = [...this.leftItems, ...this.rightItems];
      } catch (error) {
        console.error('布局加载失败:', error);
        // 错误时使用默认布局
        this.leftItems = this.blockConfig.slice(0, 3).map(item => ({ ...item }));
        this.rightItems = this.blockConfig.slice(3, 6).map(item => ({ ...item }));
        this.saveLayoutToStorage();
      }
    },
    // 根据ID获取组件名称（用于动态渲染）
    getComponentName(id) {
      const componentMap = {
        'notice': 'NoticeComponent',
        'network': 'NetworkComponent',
        'team': 'TeamComponent',
        'circuit': 'CircuitComponent',
        'alarm': 'AlarmComponent',
        'supplies': 'SuppliesComponent'
      };
      return componentMap[id] || null;
    },

    // 根据ID获取对应组件数据
    getWidgetData(id) {
      switch (id) {
        case 'notice': return this.eventData;
        case 'network': return this.sTDataInfo;
        case 'team': return this.zbGroup;
        case 'circuit': return this.XlInfoData;
        case 'alarm': return this.alarmData;
        case 'supplies': return this.yjData;
        default: return {};
      }
    },


    // 保存布局到本地存储
    saveLayoutToStorage() {
      try {
        if (!this.currentMenuId) return; // 无菜单时不保存

        const layout = {
          left: this.leftItems.map(item => item.modelConfig.id),
          right: this.rightItems.map(item => item.modelConfig.id)
        };
        // 使用菜单ID作为前缀，避免不同菜单布局覆盖
        localStorage.setItem(`dashboardLayout_${this.currentMenuId}`, JSON.stringify(layout));

        this.screenDataList.map(item => {
          if (item.orderNo === this.uniqueId) {
            item.models = this.draggableItems;
          }
        });
        // console.log('布局已保存:', layout);
      } catch (error) {
        console.error('布局保存失败:', error);
      }
    },

    // 拖拽结束事件处理
    handleDragEnd(side) {
      // 确保布局有效性
      this.ensureValidLayout(this.leftItems, this.rightItems);
      // // console.log([...this.leftItems, ...this.rightItems]);
      this.draggableItems = [...this.leftItems, ...this.rightItems];
      this.blockConfig = [...this.leftItems, ...this.rightItems];
      // 保存布局
      this.saveLayoutToStorage();
    },

    // 确保布局有效性（不重复且各三个）
    ensureValidLayout(leftItems, rightItems) {
      if (this.startItem == 'right') {
        // 1. 提取左侧ID集合（用于去重）
        const leftIds = new Set(leftItems.map(item => item.modelConfig.id));

        // 2. 处理右侧组件：先保留用户拖拽的组件，再去重
        // - 过滤掉与左侧重复的组件
        // - 保留用户拖拽到右侧的组件顺序
        const userDraggedRightItems = rightItems.filter(item => !leftIds.has(item.modelConfig.id));

        // 3. 确保左侧最多3个组件（保留用户拖拽顺序）
        const newLeftItems = leftItems.slice(0, 3);

        // 4. 计算需要补充的右侧组件
        const availableItems = this.draggableItems.filter(item => {
          // 不在左侧，也不在用户拖拽的右侧（避免重复）
          return !newLeftItems.some(left => left.modelConfig.id === item.modelConfig.id) &&
            !userDraggedRightItems.some(right => right.modelConfig.id === item.modelConfig.id);
        });

        // 5. 组合右侧组件：用户拖拽的 + 补充的（最多3个）
        const neededCount = Math.min(3 - userDraggedRightItems.length, availableItems.length);
        const Items = availableItems.slice(0, neededCount);
        const newRightItems = [...userDraggedRightItems, ...Items].slice(0, 3);

        // 6. 更新状态（使用深拷贝避免引用问题）
        this.leftItems = newLeftItems.map(item => ({ ...item }));
        this.rightItems = newRightItems.map(item => ({ ...item }));
      } else if (this.startItem == 'left') {
        // 1. 提取右侧ID集合（用于去重）
        const rightIds = new Set(rightItems.map(item => item.modelConfig.id));

        // 2. 处理左侧组件：先保留用户拖拽的组件，再去重
        // - 过滤掉与右侧重复的组件
        // - 保留用户拖拽到左侧的组件顺序
        const userDraggedLeftItems = leftItems.filter(item => !rightIds.has(item.modelConfig.id));

        // 3. 确保右侧最多3个组件（保留用户拖拽顺序）
        const newRightItems = rightItems.slice(0, 3);

        // 4. 计算需要补充的左侧组件
        const availableItems = this.draggableItems.filter(item => {
          // 不在右侧，也不在用户拖拽的左侧（避免重复）
          return !newRightItems.some(right => right.modelConfig.id === item.modelConfig.id) &&
            !userDraggedLeftItems.some(left => left.modelConfig.id === item.modelConfig.id);
        });

        // 5. 组合左侧组件：用户拖拽的 + 补充的（最多3个）
        const neededCount = Math.min(3 - userDraggedLeftItems.length, availableItems.length);
        const Items = availableItems.slice(0, neededCount);
        const newLeftItems = [...userDraggedLeftItems, ...Items].slice(0, 3);

        // 6. 更新状态（使用深拷贝避免引用问题）
        this.leftItems = newLeftItems.map(item => ({ ...item }));
        this.rightItems = newRightItems.map(item => ({ ...item }));

      }


      // console.log('布局已调整 - 左侧组件:', this.leftItems.map(item => item.modelConfig.id));
      // console.log('布局已调整 - 右侧组件:', this.rightItems.map(item => item.modelConfig.id));
      this.saveLayoutToStorage();
    },



    // 拖拽开始事件
    handleDragStart(e) {

      if (e.srcElement._prevClass == "draggable-area leftItem") {
        this.startItem = 'left';
      } else if (e.srcElement._prevClass == "draggable-area rightItem") {
        this.startItem = 'right';
      }
    },
    onMove(e, originalEvent) {
      if (!this.editMode) {
        return false;
      } else {
        return true;
      }
    },

    // 加载所有数据（模拟API请求）
    loadAllData() {
      // 实际项目中应使用axios等工具从后端获取数据
      // 此处为示例数据

      this.eventData = [
        { mesTitle: '系统升级通知', mesName: '系统将于今晚23:00-05:00进行升级维护', mesTime: '2025-06-17' },
        { mesTitle: '安全公告', mesName: '近期发现新型网络攻击，请加强安全防范', mesTime: '2025-06-16' }
      ];

      this.sTDataInfo = {
        fhqNum: 12,
        jhjNum: 36,
        posAqNum: 8
      };

      this.zbGroup = {
        num: 24,
        ydNum: 24,
        dgNum: 22,
        dgRate: '91.7%'
      };

      // 其他数据...
    },

    // 重置布局
    reset(type) {
      localStorage.removeItem('dashboardLayout');
      this.draggableItems = [...this.blockConfig];
      // console.log('布局已重置为默认', type);
    },

    // 其他业务方法...
    noticePopoverClick(notice) {
      // console.log('查看公告详情:', notice);
    },

    async sTDataClick(param) {
      let _this = this;
      if (param === "告警") {
        this.alarmPopoverShow = true;
        let _this = this;
        let type = '';
        let id = '';
        if (this.screenDataList[0].screenType == 'pos') {
          type = 'busType';
          id = this.screenDataList[0].screenId;
        } else if (this.screenDataList[0].screenType == 'line') {
          type = 'sceneId';
          id = this.configEditing.id;
        }
        await _this.$axios.get(`/protect-api/pointLine/getGjList?${type}=${id}`).then((data) => {
          if (data.data.code === "0000") {
            _this.tableData = data.data.data;
          }
        });
      } else {
        _this.map.clearOverlays();
        _this.showTopology = false;
        if (null != _this.view && undefined != _this.view) {
          _this.view.removeAllLayers();
        }
        _this.beginUrlShow = false;
        _this.urlButton = _this.beginUrl;
        if (_this.timing3) {
          clearInterval(_this.timing3);
          _this.timing3 = null;
        }
        _this.indexA = 0;
        _this.markList.map((item, index) => {
          /*        debugger*/
          let point = new BMapGL.Point(item.posLng, item.posLat);
          let myIcon = new BMapGL.Icon(item.posUrl || "http://10.93.15.92/images/zb/zhd.png", new BMapGL.Size(23, 33));
          let marker4 = new BMapGL.Marker(point, { icon: myIcon, "aa": item });  // 创建标注
          _this.map.addOverlay(marker4);
          _this.initTabchuangSt(item, param, marker4, index);
        });
        _this.markShowSt = true;
      }
      // _this.map.setZoom(13);
    },
    initTabchuangSt(param, type, marker4, index) {
      let bottom2 = require("../assets/zxx/bottom2.png");
      let text1 = '<div class="stDataDiv1">';
      let text2 = '';
      let text4 = '';
      var label = "";
      if (type === "1") {
        if (param.fhqNum == 0) {
          this.indexA++;
          return "";
        } else {
          text1 = '<div class="stDataDiv1">';
          text2 = '    <span class="stDataSpan1">防火墙：' + param.fhqNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            'font-weight: 500; color: #ffffff;"><span>' + param.alias + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-71, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.background = "transparent";
        }
      } else if (type === "2") {
        if (param.jhjNum == 0) {
          this.indexA++;
          return "";
        } else {
          text1 = '<div class="stDataDiv2">';
          text2 = '    <span class="stDataSpan1">交换机：' + param.jhjNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            'font-weight: 500; color: #ffffff;"><span>' + param.alias + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-71, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.background = "transparent";
        }
      } else if (type === "3") {
        if (param.posAqNum == 0) {
          this.indexA++;
          return "";
        } else {
          text1 = '<div class="stDataDiv3">';
          text2 = '    <span class="stDataSpan1">安全设备：' + param.posAqNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 161px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            'font-weight: 500; color: #ffffff;"><span>' + param.alias + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top:-2px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-85, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.background = "transparent";
        }
      } else if (type === "4") {
        if (param.zbNum == 0) {
          this.indexA++;
          return "";
        } else {
          text1 = '<div class="stDataDiv4">';
          text2 = '    <span class="stDataSpan1">重保人数：' + param.zbNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 148px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            'font-weight: 500; color: #ffffff;"><span>' + param.alias + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top: 20px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-79, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.background = "transparent";
        }
      } else if (type === "5") {
        if (param.upsNum == 0) {
          this.indexA++;
          return "";
        } else {
          text1 = '<div class="stDataDiv5" style="width: 220Px">';
          text2 = '    <span class="stDataSpan1" style="font-size: 17px;width: 220Px">备品备件数量：' + param.num + '类/' + param.cataNum + '件</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            'font-weight: 500; color: #ffffff;"><span>' + param.alias + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-111, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.background = "transparent";
        }
      } else if (type === "6") {
        if (param.yjclNum == 0) {
          this.indexA++;
          return "";
        } else {
          text1 = '<div class="stDataDiv6" style="width: 175px;">';
          text2 = '    <span class="stDataSpan1" style="width: 175px;">应急发电机组：' + param.yjclNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 175px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            'font-weight: 500; color: #ffffff;"><span>' + param.alias + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top:-2px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-90, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index - this.indexA].style.background = "transparent";
        }
      }
    },

    reinsuranceTeam() {
      // console.log('查看重保团队详情');
    },

    alarmPopoverClick() {
      // console.log('查看告警详情');
    },

    // 新增场景
    addNewScene() {
      this.$router.push('/new-scene-config');
    },

    // 启动自动旋转
    startAutoRotate() {
      if (this.basicConf.autoRotate.enable) {
        this.rotateTimer = setInterval(() => {
          this.rotateAngle = (this.rotateAngle + 1) % 360;
          this.map.setRotation(this.rotateAngle);
        }, this.basicConf.autoRotate.interval);
      }
    },

    // 停止自动旋转
    stopAutoRotate() {
      clearInterval(this.rotateTimer);
      this.rotateTimer = null;
    },

    // 更新当前时间
    updateCurrentTime() {
      this.currentTime = formatTime(new Date(), "yyyy-MM-dd HH:mm:ss");
    },

    // 线路列表相关方法
    toggleRouteList() {
      this.showRouteList = !this.showRouteList;
    },

    // 获取线路状态
    getRouteStatus(route) {
      switch (route.status) {
        case 'normal': return 'success';
        case 'warning': return 'warning';
        case 'error': return 'danger';
        default: return 'info';
      }
    },

    // 获取线路状态文本
    getRouteStatusText(route) {
      switch (route.status) {
        case 'normal': return '正常';
        case 'warning': return '警告';
        case 'error': return '故障';
        default: return '未知';
      }
    },

    // 处理线路点击
    handleRouteClick(route) {
      // 高亮显示选中的线路
      this.highlightRoute(route);

      // 显示线路详情
      this.showRouteDetail(route);
    },

    // 处理线路双击
    handleRouteDblClick(route) {
      // 编辑线路
      this.editRoute(route);
    },

    // 高亮显示线路
    highlightRoute(route) {
      // 清除之前的高亮
      this.routes.forEach(r => {
        if (r.polyline) {
          r.polyline.setStrokeWeight(3);
          r.polyline.setStrokeOpacity(0.8);
        }
      });

      // 高亮当前线路
      if (route.polyline) {
        route.polyline.setStrokeWeight(5);
        route.polyline.setStrokeOpacity(1.0);
      }

      // 居中显示线路
      const startPoint = new BMapGL.Point(route.startPoint[0], route.startPoint[1]);
      const endPoint = new BMapGL.Point(route.endPoint[0], route.endPoint[1]);

      // 计算中心点
      const centerPoint = new BMapGL.Point(
        (startPoint.lng + endPoint.lng) / 2,
        (startPoint.lat + endPoint.lat) / 2
      );

      // 缩放级别可以根据线路长度动态调整
      const distance = this.map.getDistance(startPoint, endPoint);
      let zoom = 13;

      if (distance > 10000) {
        zoom = 11;
      } else if (distance < 1000) {
        zoom = 15;
      }

      // this.map.centerAndZoom(centerPoint, zoom);
    },

    // 编辑线路
    editRoute(route, event) {
      // 阻止事件冒泡，避免触发表格行点击
      if (event) {
        event.stopPropagation();
      }

      // 保存当前编辑的线路ID
      this.editingRouteId = route.id;

      // 填充编辑表单
      this.editRouteForm = {
        name: route.name,
        startPoint: `${route.startPoint[0]},${route.startPoint[1]}`,
        endPoint: `${route.endPoint[0]},${route.endPoint[1]}`,
        color: route.color,
        status: route.status
      };

      // 显示编辑弹窗
      this.editRouteDialogVisible = true;
    },

    // 取消编辑线路
    cancelEditRoute() {
      this.editRouteDialogVisible = false;
      this.editRouteForm = {};
      this.editingRouteId = null;
    },

    // 保存编辑的线路
    saveEditRoute() {
      // 验证表单
      if (!this.editRouteForm.name) {
        this.$message.error('请输入线路名称');
        return;
      }

      // 解析坐标
      const startPointParts = this.editRouteForm.startPoint.split(',');
      const endPointParts = this.editRouteForm.endPoint.split(',');

      if (startPointParts.length !== 2 || endPointParts.length !== 2) {
        this.$message.error('坐标格式不正确，请使用"经度,纬度"格式');
        return;
      }

      const startPoint = [parseFloat(startPointParts[0]), parseFloat(startPointParts[1])];
      const endPoint = [parseFloat(endPointParts[0]), parseFloat(endPointParts[1])];

      // 查找并更新线路
      const routeIndex = this.routes.findIndex(r => r.id === this.editingRouteId);

      if (routeIndex !== -1) {
        // 保存旧的线路引用以便移除
        const oldPolyline = this.routes[routeIndex].polyline;

        // 更新线路数据
        this.routes[routeIndex] = {
          ...this.routes[routeIndex],
          name: this.editRouteForm.name,
          startPoint,
          endPoint,
          color: this.editRouteForm.color,
          status: this.editRouteForm.status,
          length: this.calculateRouteLength({ startPoint, endPoint }).toFixed(2)
        };

        // 移除旧线路
        if (oldPolyline) {
          this.map.removeOverlay(oldPolyline);
        }

        // 绘制新线路
        this.drawRoute(this.routes[routeIndex]);

        // 提示成功
        this.$message({
          message: `线路 "${this.editRouteForm.name}" 更新成功`,
          type: 'success'
        });

        // 关闭弹窗
        this.cancelEditRoute();
      }
    },

    // 删除线路
    deleteRoute(route, event) {
      // 阻止事件冒泡，避免触发表格行点击
      if (event) {
        event.stopPropagation();
      }

      // 保存当前要删除的线路ID和名称
      this.deleteRouteId = route.id;
      this.deleteRouteName = route.name;

      // 显示确认弹窗
      this.deleteRouteDialogVisible = true;
    },

    // 确认删除线路
    confirmDeleteRoute() {
      // 查找并删除线路
      const routeIndex = this.routes.findIndex(r => r.id === this.deleteRouteId);

      if (routeIndex !== -1) {
        // 移除地图上的线路
        if (this.routes[routeIndex].polyline) {
          this.map.removeOverlay(this.routes[routeIndex].polyline);
        }

        // 从数组中删除
        this.routes.splice(routeIndex, 1);

        // 提示成功
        this.$message({
          message: `线路 "${this.deleteRouteName}" 删除成功`,
          type: 'success'
        });
      }

      // 关闭弹窗
      this.deleteRouteDialogVisible = false;
      this.deleteRouteId = null;
      this.deleteRouteName = '';
    },

    // 筛选线路
    filterRoutes() {
      this.currentPage = 1;
    },

    // 排序线路
    sortRoutes(field) {
      if (this.sortField === field) {
        // 切换排序方向
        this.sortOrder = this.sortOrder === 'ascending' ? 'descending' : 'ascending';
      } else {
        // 设置新的排序字段，默认升序
        this.sortField = field;
        this.sortOrder = 'ascending';
      }

      // 重置分页
      this.currentPage = 1;
    },

    // 处理每页数量变化
    handleSizeChange(newSize) {
      this.pageSize = newSize;
    },

    // 处理当前页变化
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
    },
    openSetupPoint(type, point) {
      this.pointsType = type;
      this.form = point;
      this.activeName = type == '起点' ? 'startPoint' : type == '终点' ? 'endPoint' : 'line';
      this.setupPointDialog = true;
      this.$axios.get(`/protect-api/circuit/getCircuitNameByName?circuitName=${''}`).then((data) => {
        this.latitudeData = data.data.data;
      });
    },
    // 站点图片上传
    // 处理图标上传成功
    // 自定义上传方法
    customUpload(params) {
      const { file, onSuccess, onError, onProgress } = params;
      const formData = new FormData();
      formData.append('file', file);

      // 添加额外参数
      const isIcon = file.name.includes('icon') || file.name.includes('logo');
      formData.append('fileType', isIcon ? 'icon' : 'image');
      formData.append('bucket', 'site-resources');

      // 创建XMLHttpRequest对象
      const xhr = new XMLHttpRequest();

      // 监听上传进度
      xhr.upload.addEventListener('progress', (e) => {
        if (e.total > 0) {
          const percentage = Math.round((e.loaded / e.total) * 100);
          if (isIcon) {
            this.uploadIconProgress = percentage;
          } else {
            this.uploadImageProgress = percentage;
          }
          onProgress({ percent: percentage });
        }
      });

      // 监听请求完成
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            onSuccess(response);
          } catch (e) {
            onError(new Error('解析响应数据失败'));
          }
        } else {
          onError(new Error(xhr.statusText));
        }

        // 重置上传状态
        if (isIcon) {
          this.uploadingIcon = false;
        } else {
          this.uploadingImage = false;
        }
      });

      // 监听请求错误
      xhr.addEventListener('error', () => {
        onError(new Error('上传请求失败'));
        if (isIcon) {
          this.uploadingIcon = false;
        } else {
          this.uploadingImage = false;
        }
      });

      // 监听请求取消
      xhr.addEventListener('abort', () => {
        onError(new Error('上传已取消'));
        if (isIcon) {
          this.uploadingIcon = false;
        } else {
          this.uploadingImage = false;
        }
      });

      // 配置请求
      xhr.open('POST', this.uploadUrl, true);

      // 设置请求头（注意：不要设置Content-Type，让浏览器自动设置为multipart/form-data）
      Object.keys(this.headers).forEach(key => {
        xhr.setRequestHeader(key, this.headers[key]);
      });

      // 发送请求
      xhr.send(formData);
    },

    // 处理上传进度
    handleUploadProgress(event, file, fileList) {
      const percentage = Math.round((event.loaded / event.total) * 100);
      if (file.name.includes('icon')) {
        this.uploadIconProgress = percentage;
      } else {
        this.uploadImageProgress = percentage;
      }
    },

    // 处理图标上传成功
    handleIconSuccess(type) {
      return (response, file, fileList) => {
        this.uploadingIcon = false;
        this.uploadIconProgress = 0;

        if (response && response.code === "0000") {
          this.routerForm[type].iconUrl = response.data.fileUrl || `${this.serverBaseUrl}/files/${response.data.fileId}`;
          this.$message.success('图标上传成功');
        } else {
          this.$message.error('图标上传失败: ' + (response.message || '未知错误'));
        }
      };
    },

    // 处理图片上传成功
    handleImageSuccess(type) {
      return (response, file, fileList) => {
        if (response && response.code === "0000") {
          this.routerForm[type].imgUrl = response.data.fileUrl || `${this.serverBaseUrl}/files/${response.data.fileId}`;
          this.$message.success('图标上传成功');
        } else {
          this.$message.error('图标上传失败: ' + (response.message || '未知错误'));
        }
      };
    },

    // 处理上传错误
    handleUploadError(error, file, fileList) {
      this.$message.error('上传出错，请稍后再试');
      console.error('上传错误:', error);
    },

    // 上传前检查图标
    beforeIconUpload(file, type) {
      // console.log(type);

      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('图标只能是JPG/PNG格式!');
      }
      if (!isLt2M) {
        this.$message.error('图标大小不能超过2MB!');
      }
      return isJPG && isLt2M;
    },

    // 上传前检查图片
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error('图片只能是JPG/PNG格式!');
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!');
      }
      return isJPG && isLt5M;
    },

    // 移除图标
    removeIcon(type) {
      // this.$axios.post(`/protect-api/api/file/delete?imgUrl=${this.form.iconUrl}`,).then((data) => {
      //   if (data.data.code === '0000') {
      this.routerForm[type].iconUrl = '';
      this.$message.success('图标删除成功');
      // }  else {
      //   this.$message.error('图标删除失败: ' + data.data.message);
      // }
      // });
    },

    // 移除图片
    removeImage(type) {
      // this.$axios.post(`/protect-api/api/file/delete?imgUrl=${this.form.imgUrl}`,).then((data) => {
      //   if (data.data.code === '0000') {
      this.routerForm[type].imgUrl = '';
      this.$message.success('图标删除成功');
      // }  else {
      //   this.$message.error('图标删除失败: ' + data.data.message);
      // }
      // });
    },
    // 处理图标上传成功
    handleFileSuccess(type) {
      return (response, file, fileList) => {
        this.uploadingIcon = false;
        this.uploadIconProgress = 0;
        if (response && response.code === "0000") {
          this.routerForm[type].personUrl = response.data.fileUrl || `${this.serverBaseUrl}/files/${response.data.fileId}`;
          this.$message.success('文件上传成功');
        } else {
          this.$message.error('文件上传失败: ' + (response.message || '未知错误'));
        }
      };
    },
    handleResourceFileSuccess(type) {
      return (response, file, fileList) => {
        this.uploadingIcon = false;
        this.uploadIconProgress = 0;

        if (response && response.code === "0000") {
          this.routerForm[type].resourceUrl = response.data.fileUrl || `${this.serverBaseUrl}/files/${response.data.fileId}`;
          this.$message.success('文件上传成功');
        } else {
          this.$message.error('文件上传失败: ' + (response.message || '未知错误'));
        }
      };
    },

    // 处理上传错误
    handleFileError(error, file, fileList) {
      this.$message.error('上传出错，请稍后再试');
      console.error('上传错误:', error);
    },
    cancelAdd() {
      this.addRouter = false;
      if (this.form.imgUrl && this.form.imgUrl !== '') {
        this.removeImage();
      }
      if (this.form.iconUrl && this.form.iconUrl !== '') {
        this.removeIcon();
      }
      this.form = {};
      this.routerForm = {
        "startPoint": {
          "pointName": "",
          "pointAlias": "",
          "longitude": "",
          "latitude": "",
          "iconUrl": "",
          "imgUrl": "",
          "pointType": "",
          "personUrl": '',
          "resourceUrl": ''
        },
        "endPoint": {
          "pointName": "",
          "pointAlias": "",
          "longitude": "",
          "latitude": "",
          "iconUrl": "",
          "imgUrl": "",
          "pointType": "",
          "personUrl": '',
          "resourceUrl": ''
        },
        "lineName": "",
        "businessTypeList": "5",
        "lineColor": "#FFF387",
        "lineType": "info",
        "businessType": '',
        'founder': 'admin',
        'circuitId': ''
      };
      this.addMapLayers();
      // this.map.clearOverlays();
    },
    cancelSiteChanges() {
      this.routerForm = {
        "startPoint": {
          "pointName": "",
          "pointAlias": "",
          "longitude": "",
          "latitude": "",
          "iconUrl": "",
          "imgUrl": "",
          "pointType": "",
          "personUrl": '',
          "resourceUrl": ''
        },
        "endPoint": {
          "pointName": "",
          "pointAlias": "",
          "longitude": "",
          "latitude": "",
          "iconUrl": "",
          "imgUrl": "",
          "pointType": "",
          "personUrl": '',
          "resourceUrl": ''
        },
        "lineName": "",
        "businessTypeList": "5",
        "lineColor": "#FFF387",
        "lineType": "info",
        "businessType": '',
        'founder': 'admin',
        'circuitId': ''

      };
      this.setupPointDialog = false;
      
      this.activeTab = ''
      this.circuitTabs=[]  
    },
    confirmSiteChanges(val) {
      if (this.routerForm.lineId || this.routerForm.startPoint.posId) {
        console.log('updata')
        this.updatePoint();
      } else
      {
        console.log(this.routerForm.startPoint)
        if (this.routerForm.startPoint.pointType == 'single') {
          this.addRouters();
        }        }

    },
    getAllLines(busType) {
      this.$axios.get(`/protect-api/pointLine/getAllLines/${busType}`).then((data) => {
        this.tableData = data.data.data;
      });

    },
    editRouter(row) {
      console.log(row)
      this.circuitTabs=[]
      // this.openRouter();
      this.routerForm = JSON.parse(JSON.stringify(row))
      this.routerForm.lineColor = JSON.parse(JSON.stringify(row)).lineColor.split(',')[0]
      //  (JSON.stringify(row));
      if (row.circuitAndCircuitRoutList.length === 0) {
        this.circuitTabs = []
      } else {
        row.circuitAndCircuitRoutList.forEach(item => {
          const { circuitId, circuitName } = item.circuit;
          const routerData = item.circuitRoutList; // circuitRoutList 就是 routerData

          this.circuitTabs.push({
            circuitId,
            circuitName,
            routerData
          });
        });
        this.activeTab = this.circuitTabs[0].circuitId
      }
      this.setupPointDialog = true;
      this.$axios.get(`/protect-api/circuit/getCircuitNameByName?circuitName=${''}`).then((data) => {
        this.latitudeData = data.data.data;
      });
    },
    async handleCircuitChange(circuitId) {
      console.log(circuitId)
      // 检查是否已经存在该电路的Tab
      const existingTab = this.circuitTabs.find(tab => tab.circuitId === circuitId);

      if (!existingTab) {
        // 获取电路数据
        const routerData = await this.latitudeRouter(circuitId);
        // 获取电路名称
        const circuit = this.latitudeData.find(item => item.circuitId === circuitId);
        const circuitName = circuit ? circuit.circuitName : `电路-${circuitId}`;

        // 添加新Tab
        this.circuitTabs.push({
          circuitId,
          circuitName,
          routerData
        });
      } 

      // 激活当前Tab
      this.activeTab = circuitId;
    },

    removeTab(circuitId) {
      // 移除Tab
      this.circuitTabs = this.circuitTabs.filter(tab => tab.circuitId !== circuitId);

      // 如果删除的是当前激活的Tab，则激活最后一个Tab
      if (this.activeTab === circuitId) {
        this.activeTab = this.circuitTabs.length > 0
          ? this.circuitTabs[this.circuitTabs.length - 1].circuitId
          : '';
      }
    },
    async latitudeRouter(val) {
      try {
        const res = await this.$axios.get(`/protect-api/circuit/getCircuitRouteById?circuitId=${val || ''}`) // 替换为实际的API调用
        return res.data.data; // 返回获取的路由数据
      } catch (error) {
        console.error('获取路由信息失败:', error);
        return [];
      }
    },

    openRouter() {
      this.addRouter = true;
      this.activeName = "startPoint";
      this.routerForm = {
        "startPoint": {
          "pointName": "",
          "pointAlias": "",
          "longitude": "",
          "latitude": "",
          "iconUrl": "",
          "imgUrl": "",
          "pointType": "",
        },
        "endPoint": {
          "pointName": "",
          "pointAlias": "",
          "longitude": "",
          "latitude": "",
          "iconUrl": "",
          "imgUrl": "",
          "pointType": "",
        },
        "lineName": "",
        "businessTypeList": "5",
        "color": "#FFF387",
        "lineType": "info",
        "bandWidth": '',
        "zxNum": ''
      };
    },
    deleteRouter(row) {
      console.log(row)
      this.$confirm('此操作将永久删除该线路, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        if (row.startPoint.pointType == 'single') {
          this.$axios.delete(`/protect-api/pointLine/deleteSeparatePoint/${row.startPoint.posId}`).then((data) => {
            if (data.data.code == '0000') {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.getAllLines(this.setUpType == '站点' ? this.posDataId : this.lineDataId);
              this.addMapLayers();
            } else {
              this.$message.error('删除失败！');
            }
          });
        } else {
          let params = {
            startPointId: row.startPoint.posId,
            endPointId: row.endPoint.posId,
            lineId: row.lineId
          };
          this.$axios.post(`/protect-api/pointLine/deletePointLine`, params).then((data) => {
            if (data.data.code == '0000') {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.getAllLines(this.setUpType == '站点' ? this.posDataId : this.lineDataId);
              this.addMapLayers();
            } else {
              this.$message.error('删除失败！');
            }
          });
        }

      });
      // .catch(() => {
      //   this.$message({
      //     type: 'info',
      //     message: '已取消删除'
      //   });          
      // });
    },
    addRouters() {
      if (this.routerForm.startPoint.longitude == '' || this.routerForm.endPoint.longitude == '') {
        let params = {
          startPoint: this.routerForm.startPoint.longitude == '' ? this.routerForm.endPoint : this.routerForm.startPoint,
          businessType: this.setUpType == '站点' ? this.posDataId : this.lineDataId,
          // circuit:{},
          // circuitRouts: this.circuitTabs || []
        };
        this.$axios.post("/protect-api/pointLine/createSeparatePoint", params).then((data) => {
          if (data.data.code === '0000') {
            this.$message.success('添加成功');
            this.addRouter = false;
            this.getAllLines(this.setUpType == '站点' ? this.posDataId : this.lineDataId);
            this.addMapLayers();
            this.setupPointDialog = false;
          } else {
            this.$message.error('添加失败: ' + data.data.message);
          }

        });
      } else {
        this.routerForm.businessType = this.setUpType == '站点' ? this.posDataId : this.lineDataId;
        this.routerForm.lineColor = this.routerForm.color + ',#DA8EFF,#FFFFFF';
        const circuitIds = this.circuitTabs.map(tab => tab.circuitId);
        this.routerForm.circuitIds = circuitIds
        this.$axios.post("/protect-api/pointLine/createPointLine", this.routerForm).then((data) => {
          if (data.data.code === '0000') {
            this.$message.success('添加成功');
            this.addRouter = false;
            this.getAllLines(this.setUpType == '站点' ? this.posDataId : this.lineDataId);
            this.addMapLayers();
            this.setupPointDialog = false;
          } else {
            this.$message.error('添加失败: ' + data.data.message);
          }

        });
      }

    },
    updatePoint() {

      if (this.routerForm.startPoint.pointType !== 'single') {
        this.routerForm.businessType = this.setUpType == '站点' ? this.posDataId : this.lineDataId;
        const circuitIds = this.circuitTabs.map(tab => tab.circuitId);
        this.routerForm.circuitIds = circuitIds
        this.$axios.put("/protect-api/pointLine/updatePointLine", this.routerForm).then((data) => {
          if (data.data.code === '0000') {
            this.$message.success('修改成功');
            this.addRouter = false;
            this.getAllLines(this.setUpType == '站点' ? this.posDataId : this.lineDataId);
            this.addMapLayers();
            this.setupPointDialog = false;
          } else {
            this.$message.error('修改失败: ' + data.data.msg);
          }

        });
      } else {
        let params = {
          startPoint: this.routerForm.startPoint,
          businessType: this.setUpType == '站点' ? this.posDataId : this.lineDataId
        };
        this.$axios.put("/protect-api/pointLine/updatePoint", params).then((data) => {
          // // console.log(data);
          if (data.data.code === '0000') {
            this.$message.success('修改成功');
            this.addRouter = false;
            this.getAllLines(this.setUpType == '站点' ? this.posDataId : this.lineDataId);
            this.addMapLayers();
            this.setupPointDialog = false;
          } else {
            this.$message.error('修改失败: ' + data.data.message);
          }

        });
      }
    },
    handleClick() {
      if (this.activeName == 'startPoint') {
        this.form = this.routerForm.startPoint;
        this.pointsType = '起点设置';
      } else if (this.activeName == 'endPoint') {
        this.form = this.routerForm.endPoint;
        this.pointsType = '终点设置';
      } else {
        this.form = this.routerForm;
        this.pointsType = '线路设置';
      }
    },
    // 修改：菜单切换时设置当前菜单ID并加载布局
    changeMenu(item) {
      this.uniqueId = item.orderNo;
      this.currentMenuId = item.orderNo; // 设置当前菜单ID
      if (item.screenType == 'pos') {
        this.getZJData('busType', item.screenId);
      } else if (item.screenType == 'line') {
        this.getZJData('sceneId', this.configEditing.id);
      }
      this.$nextTick(() => {
        this.blockConfig = item.models;
        this.loadLayoutFromStorage(); // 加载当前菜单的布局
      });
    },
    openMenu(e, item) {
      // console.log(e,item);
      this.visible = true;
      this.top = e.offsetY + 40;
      this.left = e.pageX;
      this.rightClickItem = item;
    },// 关闭右键菜单
    closeMenu() {
      this.visible = false;
    },
    copyItem(item) {
      let copyItem = JSON.parse(JSON.stringify(item));
      copyItem.uniqueId = this.screenDataList.length + 1;
      copyItem.screenId = '';
      copyItem.orderNo = this.screenDataList.length + 1;
      this.screenDataList.push(copyItem);

    },
    deleteItem(item) {
      this.screenDataList = this.screenDataList.filter(item1 => item1.screenId != item.screenId);
    },
    addUniqueId(arr) {
      let id = 0;
      let _this = this;
      let blockConfig = [
        {
          modelConfig: {
            id: 'notice',
            title: '公告信息',
            className: 'mapLeftTop1',
            component: 'NoticeComponent',
            actions: [{ id: 'refresh', icon: 'el-icon-refresh', handler: () => this.refreshData('notice') }],
          },
          modelId: "",
          modelName: "公告信息",
          modelType: "公告信息",
          orderNo: 2,
          visibleState: "1"
        },
        {
          modelConfig: {
            id: 'network',
            title: '数通设备',
            className: 'mapLeftTop2',
            component: 'NetworkComponent',
            actions: [{ id: 'reset', icon: 'el-icon-refresh-right', handler: () => this.reset('network') }],
          },
          modelId: "",
          modelName: "数通设备",
          modelType: "数通设备",
          orderNo: 2,
          visibleState: "1"
        },
        {
          modelConfig: {
            id: 'team',
            title: '重保团队',
            className: 'mapLeftTop3',
            component: 'TeamComponent',
            actions: [{ id: 'view', icon: 'el-icon-view', handler: () => this.reinsuranceTeam() }],
          },
          modelId: "",
          modelName: "重保团队",
          modelType: "重保团队",
          orderNo: 2,
          visibleState: "1"
        }, {
          modelConfig: {
            id: 'supplies',
            title: '应急物资',
            className: 'mapRightTop2',
            component: 'SuppliesComponent',
            actions: [{ id: 'check', icon: 'el-icon-check', handler: () => this.checkSupplies() }],
          },
          modelId: "",
          modelName: "应急物资",
          modelType: "应急物资",
          orderNo: 2,
          visibleState: "1"
        },
        {
          modelConfig: {
            id: 'alarm',
            title: '当前告警数量',
            className: 'mapRightTop1',
            component: 'AlarmComponent',
            actions: [{ id: 'detail', icon: 'el-icon-document', handler: () => this.alarmPopoverClick() }],
          },
          modelId: "",
          modelName: "当前告警数量",
          modelType: "当前告警数量",
          orderNo: 2,
          visibleState: "1"
        }, {
          modelConfig: {
            id: 'circuit',
            title: '线路状态',
            className: 'mapRightTop3',
            component: 'CircuitComponent',
            actions: [{ id: 'filter', icon: 'el-icon-filter', handler: () => this.filterCircuit() }],
          },
          modelId: "",
          modelName: "线路状态",
          modelType: "线路状态",
          orderNo: 2,
          visibleState: "1"
        },
      ];
      return arr.map(item => {
        // console.log(item.models);

        return {
          ...item,
          uniqueId: `${id++}`,  // 生成格式为"id_1"、"id_2"的唯一ID
          // models: item.models.length>0 ? item.models : blockConfig
          models: item.models
        };
      });
    },
    // 初始化自定义右键菜单
    initContextMenu() {
      const menu = document.createElement('div');
      menu.id = 'context-menu';
      menu.className = 'custom-context-menu';
      menu.style.cssText = `
        display: none;
        position: fixed;
        background-color: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
        padding: 5px 0;
        z-index: 1000;
        color: #000;
        min-width: 120px;
        font-family: Arial, sans-serif;
        font-size: 14px;
      `;

      // 创建菜单项的函数
      const createMenuItem = (text, onClick) => {
        const item = document.createElement('div');
        item.className = 'context-menu-item';
        item.textContent = text;
        item.style.cssText = `
          padding: 6px 20px;
          cursor: pointer;
        `;
        item.onmouseover = () => item.style.backgroundColor = '#f1f1f1';
        item.onmouseout = () => item.style.backgroundColor = '';
        item.onclick = () => {
          onClick();
          menu.style.display = 'none';
        };
        return item;
      };

      // 添加菜单项
      menu.appendChild(createMenuItem('编辑折线', () => this.startEditMode()));
      menu.appendChild(createMenuItem('新增位置点', (e) => this.startAddPoint(e)));
      // menu.appendChild(createMenuItem('添加顶点', () => this.addVertexAtClickPosition()));

      const separator = document.createElement('div');
      separator.style.cssText = `
        margin: 5px 0;
        border-bottom: 1px solid #eee;
      `;
      menu.appendChild(separator);

      // menu.appendChild(createMenuItem('删除折线', () => this.deleteCurrentPolyline()));

      document.body.appendChild(menu);

      // 点击其他区域关闭菜单
      document.addEventListener('click', (e) => {
        if (!menu.contains(e.target)) {
          menu.style.display = 'none';
        }
      });
    },
    deleteCurrentPolyline() {
      if (this.selectedPolyline) {
        this.map.removeOverlay(this.selectedPolyline);
        this.selectedPolyline = null;
        this.isEditMode = false;

        this.updatePolyline();
      }
    },
    // 处理折线右键点击
    handlePolylineRightClick(e) {
      // 显示自定义右键菜单
      const contextMenu = document.getElementById('context-menu');
      contextMenu.style.display = 'block';
      contextMenu.style.left = e.pixel.x + 'px';
      contextMenu.style.top = e.pixel.y + 'px';

      // 记录右键点击位置，可能用于添加新顶点
      window.rightClickPosition = e.point;
    },
    // 开始编辑模式
    startEditMode() {
      if (!this.selectedPolyline) return;
      console.log(this.selectedPolyline)
      this.nowChangeLineData = this.selectedPolyline.lineData;
      const path = this.selectedPolyline.getPath();
      this.originalStart = path[0];
      this.originalEnd = path[path.length - 1];

      // 启用折线编辑
      this.selectedPolyline.enableEditing(true);

      // 清除之前的事件监听器
      this.clearPolylineListeners();

      // 添加拖拽过程中的实时更新事件
      this.selectedPolyline.addEventListener('lineupdate', () => {
        if (this.isLockingEndpoints) return; // 防止循环调用

        this.throttledUpdatePolyline();
        this.forceMapRedraw();
      });

      // console.log('进入编辑模式，起点/终点不可拖拽');
    },
    //新增位置点
    startAddPoint() {
      if (this.selectedPolyline) {
        const path = this.selectedPolyline.getPath();
        if (path.length < 2) {
          alert('折线至少需要两个点才能添加新点');
          return;
        }
        
        // 随机选择一个线段
        const segmentIndex = Math.floor(Math.random() * (path.length - 1));
        const p1 = path[segmentIndex];
        const p2 = path[segmentIndex + 1];
        
        // 将点放在线段中间（ratio设为0.5）
        const ratio = 0.5;
        const newPoint = new BMapGL.Point(
          p1.lng + (p2.lng - p1.lng) * ratio,
          p1.lat + (p2.lat - p1.lat) * ratio
        );
        
        // 更新折线
        path.splice(segmentIndex + 1, 0, newPoint);
        this.selectedPolyline.setPath(path);
        
        // 添加标记点并设置动画和拖拽
        const marker = new BMapGL.Marker(newPoint, {
          enableDragging: false,  // 先禁用，稍后通过bindMarkerDragEvents启用
          raiseOnDrag: true  // 拖拽时提升层级
        });
        
        marker.setAnimation(BMAP_ANIMATION_BOUNCE);
        this.map.addOverlay(marker);
        
        // 保存新增的标记点及其在路径中的索引
        if (!this.addedMarkers) {
          this.addedMarkers = [];
        }
        
        // 存储标记点及其在路径中的位置
        this.addedMarkers.push({
          marker,
          pathIndex: segmentIndex + 1
        });
        
        // 绑定拖拽事件，确保点只能在线上拖拽
        this.bindMarkerDragEvents(marker, segmentIndex + 1);
        // 显示属性设置对话框
        this.setupDialog = true;
        // this.showPointPropertiesDialog(marker, newPoint, segmentIndex + 1);
      }
    },
    // 显示点属性设置对话框
    showPointPropertiesDialog(marker, point, pathIndex) {
      // 创建对话框容器
      const dialog = document.createElement('div');
      dialog.className = 'point-properties-dialog';
      dialog.style.cssText = `
        position: fixed;
        background-color: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
        padding: 15px;
        z-index: 1001;
        width: 280px;
        font-family: Arial, sans-serif;
        font-size: 14px;
        top: 20%;
        left: 85%;
        transform: translate(-50%, -50%);
      `;
      
      // 设置对话框内容
      dialog.innerHTML = `
        <h3 style="margin-top: 0; margin-bottom: 15px;color:#000">设置点属性</h3>
        <div style="margin-bottom: 10px;">
          <label style="display: block; margin-bottom: 5px;">点名称:</label>
          <input type="text" id="point-name" style="width: 100%; padding: 5px; box-sizing: border-box;" placeholder="输入点名称">
        </div>
        <div style="margin-bottom: 10px;">
          <label style="display: block; margin-bottom: 5px;">资源类型:</label>
          <select id="resource-type" style="width: 100%; padding: 5px; box-sizing: border-box;">
            <option value="default">默认</option>
            <option value="hospital">医院</option>
            <option value="school">学校</option>
            <option value="factory">工厂</option>
            <option value="store">商店</option>
          </select>
        </div>
        <div style="text-align: right;">
          <button id="cancel-btn" style="margin-right: 10px; padding: 5px 10px;">取消</button>
          <button id="save-btn" style="padding: 5px 10px; background-color: #4CAF50; color: white; border: none;">保存</button>
        </div>
      `;
      
      // 添加到页面
      document.body.appendChild(dialog);
      
      // 获取DOM元素
      const cancelBtn = dialog.querySelector('#cancel-btn');
      const saveBtn = dialog.querySelector('#save-btn');
      const nameInput = dialog.querySelector('#point-name');
      const typeSelect = dialog.querySelector('#resource-type');
      
      // 取消按钮事件
      cancelBtn.addEventListener('click', () => {
        document.body.removeChild(dialog);
        // 移除临时添加的标记点
        this.map.removeOverlay(marker);
        // 恢复折线
        const path = this.selectedPolyline.getPath();
        path.splice(pathIndex, 1);
        this.selectedPolyline.setPath(path);
      });
      
      // 保存按钮事件
      saveBtn.addEventListener('click', () => {
        const pointName = nameInput.value.trim() || '未命名点';
        const resourceType = typeSelect.value;
        
        // 根据资源类型设置不同的图标
        const icon = this.getIconByType(resourceType);
        marker.setIcon(icon);
        
        // 创建信息窗口
        const infoWindow = new BMapGL.InfoWindow(`
          <div style="font-family: Arial, sans-serif;">
            <h4>${pointName}</h4>
            <p>资源类型: ${this.getTypeName(resourceType)}</p>
            <p>坐标: ${point.lng.toFixed(6)}, ${point.lat.toFixed(6)}</p>
          </div>
        `);
        
        // 点击标记点显示信息窗口
        marker.addEventListener('click', () => {
          this.map.openInfoWindow(infoWindow, point);
        });
        
        // 保存点属性
        marker.pointData = {
          name: pointName,
          type: resourceType,
          position: point
        };
        
        // 保存新增的标记点及其在路径中的索引
        if (!this.addedMarkers) {
          this.addedMarkers = [];
        }
        
        this.addedMarkers.push({
          marker,
          pathIndex
        });
        
        // 绑定拖拽事件，确保点只能在线上拖拽
        this.bindMarkerDragEvents(marker, pathIndex);
        
        // 关闭对话框
        document.body.removeChild(dialog);
      });
    },

    // 绑定标记点的拖拽事件，确保点只能在线上拖拽
    bindMarkerDragEvents(marker, pathIndex) {
      // 重新启用百度地图的拖拽，但在拖拽事件中进行约束
      marker.enableDragging();

      let isDragging = false;

      // 拖拽开始
      marker.addEventListener('dragstart', (e) => {
        isDragging = true;
        console.log('拖拽开始，点索引:', pathIndex, '位置:', e.point);
      });

      // 拖拽过程中 - 只更新标记点位置，不修改折线路径
      marker.addEventListener('dragging', (e) => {
        if (!isDragging) return;

        // 计算点在线段上的约束位置
        const constrainedPosition = this.constrainPointToPolyline(e.point, pathIndex);

        if (constrainedPosition && constrainedPosition.lng && constrainedPosition.lat) {
          // 只更新标记点位置，不修改折线路径
          marker.setPosition(constrainedPosition);
          console.log('拖拽中，约束位置:', constrainedPosition);
        }
      });

      // 拖拽结束 - 最终更新折线路径
      marker.addEventListener('dragend', (e) => {
        isDragging = false;

        // 最终确保位置正确并更新折线路径
        const finalPosition = this.constrainPointToPolyline(e.point, pathIndex);
        if (finalPosition && finalPosition.lng && finalPosition.lat) {
          // 更新标记点位置
          marker.setPosition(finalPosition);

          // 更新折线路径 - 直接替换对应索引的点
          const path = this.selectedPolyline.getPath();
          if (pathIndex >= 0 && pathIndex < path.length) {
            // 创建新的路径数组，确保更新生效
            const newPath = [...path];
            newPath[pathIndex] = finalPosition;
            this.selectedPolyline.setPath(newPath);
            console.log('拖拽结束，最终位置:', finalPosition, '路径索引:', pathIndex);
          } else {
            console.error('路径索引超出范围:', pathIndex, '路径长度:', path.length);
          }
        }
      });
    },

    // 计算点在线段上的投影位置，确保点始终在线上
    constrainPointToPolyline(point, pathIndex) {
      const path = this.selectedPolyline.getPath();

      // 确保索引有效
      if (pathIndex < 1 || pathIndex >= path.length - 1) {
        console.error('无效的点索引:', pathIndex, '路径长度:', path.length);
        return point;
      }

      // 找到距离拖拽点最近的线段
      let closestSegmentIndex = -1;
      let minDistance = Infinity;

      // 遍历所有线段，找到最近的一个
      for (let i = 0; i < path.length - 1; i++) {
        const p1 = path[i];
        const p2 = path[i + 1];

        // 计算点到线段的距离
        const projectedPoint = this.calculateProjection(point, p1, p2);
        const distance = this.calculateDistance(point, projectedPoint);

        if (distance < minDistance) {
          minDistance = distance;
          closestSegmentIndex = i;
        }
      }

      if (closestSegmentIndex === -1) {
        console.error('未找到最近线段');
        return point;
      }

      // 使用最近线段进行约束
      const p1 = path[closestSegmentIndex];
      const p2 = path[closestSegmentIndex + 1];

      const constrainedPoint = this.calculateProjection(point, p1, p2);
      console.log('约束计算: 原点', point, '最近线段:', closestSegmentIndex, '约束后', constrainedPoint);
      return constrainedPoint;
    },

    // 计算点在两点确定的线段上的投影位置
    calculateProjection(point, p1, p2) {
      // 计算线段向量
      const dx = p2.lng - p1.lng;
      const dy = p2.lat - p1.lat;

      // 计算线段长度的平方
      const segmentLengthSquared = dx * dx + dy * dy;

      // 如果线段长度为0，直接返回p1
      if (segmentLengthSquared === 0) {
        return p1;
      }

      // 计算点到p1的向量
      const px = point.lng - p1.lng;
      const py = point.lat - p1.lat;

      // 计算点积
      const dotProduct = px * dx + py * dy;

      // 计算投影比例
      let t = dotProduct / segmentLengthSquared;

      // 限制t在[0,1]范围内，确保点在线段上而不是延长线上
      t = Math.max(0, Math.min(1, t));

      // 计算投影点
      const projectedPoint = new BMapGL.Point(
        p1.lng + t * dx,
        p1.lat + t * dy
      );

      console.log('投影计算结果: t=', t, '投影点:', projectedPoint);
      return projectedPoint;
    },

    // 计算两点之间的距离
    calculateDistance(point1, point2) {
      const dx = point2.lng - point1.lng;
      const dy = point2.lat - point1.lat;
      return Math.sqrt(dx * dx + dy * dy);
    },

    // 重新同步所有标记点的路径索引
    resyncMarkerIndices() {
      if (!this.addedMarkers || !this.selectedPolyline) return;

      const path = this.selectedPolyline.getPath();

      this.addedMarkers.forEach(markerInfo => {
        const markerPosition = markerInfo.marker.getPosition();

        // 在路径中找到最接近的点的索引
        let closestIndex = -1;
        let minDistance = Infinity;

        for (let i = 1; i < path.length - 1; i++) { // 跳过起点和终点
          const distance = this.calculateDistance(markerPosition, path[i]);
          if (distance < minDistance) {
            minDistance = distance;
            closestIndex = i;
          }
        }

        if (closestIndex !== -1) {
          markerInfo.pathIndex = closestIndex;
          console.log('重新同步标记点索引:', closestIndex);
        }
      });
    },

    // 清除所有添加的标记点
    clearAddedMarkers() {
      if (this.addedMarkers) {
        this.addedMarkers.forEach(markerInfo => {
          if (markerInfo.marker) {
            this.map.removeOverlay(markerInfo.marker);
          }
        });
        this.addedMarkers = [];
        console.log('已清除所有添加的标记点');
      }
    },

    // 类型选择相关方法
    testDialog() {
      console.log('测试按钮被点击了！');
      this.showTypeDialog = true;
      console.log('showTypeDialog 设置为:', this.showTypeDialog);
    },

    openTypeDialog() {
      console.log('点击打开类型选择对话框');
      console.log('当前 showTypeDialog 值:', this.showTypeDialog);
      this.showTypeDialog = true;
      console.log('设置后 showTypeDialog 值:', this.showTypeDialog);

      // 强制更新视图
      this.$nextTick(() => {
        console.log('nextTick 后 showTypeDialog 值:', this.showTypeDialog);
      });
    },
    // 获取空间资源数据
    getSelectorData(val, type) {
      const fd = new FormData();
      fd.append('type',type)
      fd.append('pageNum', this.tableDataPageNum)
      fd.append('pageSize', this.tableDataPageSize)
      return this.$axios.post('/protect-api/res/getSpaceResources', fd);
    },
    // 获取设备资源数据
    getEquipmentResources(val, type) {
      const fd = new FormData();
      fd.append('type',type)
      fd.append('pageNum', this.tableDataPageNum)
      fd.append('pageSize', this.tableDataPageSize)
      return this.$axios.post('/protect-api/res/getEquipmentResources', fd);
      
    },
    getCard(val){
      const fd = new FormData();
      fd.append('resourceId',val)
      fd.append('pageNum', this.tableDataPageNum)
      fd.append('pageSize', this.tableDataPageSize)
      return this.$axios.post('/protect-api/res/getCard', fd);
      
    },
    getPort(val) {
      const fd = new FormData();
      fd.append('resourceId',val)
      fd.append('pageNum', this.tableDataPageNum)
      fd.append('pageSize', this.tableDataPageSize)
      return this.$axios.post('/protect-api/res/getPort', fd);
    },
    // 所属专业
    onProfessionChange(value) {
      console.log('选择专业:', value);
      this.selectedDeviceType = '';
      this.profession = value
      this.deviceTypeOptions = [];
      this.cascadeTableData = [];
      // 只有在选择设备资源时才重置板卡和端口
      if (value === 'device') {
        this.selectedBoard = '';
        this.selectedPort = '';
      }

      // 根据专业加载设备类型
      if (value === 'space') {
        this.deviceTypeOptions = [
          { value: 'SPC_STATION', label: '局站' },
          { value: 'SPC_ROOM', label: '机房' }
        ];
      } else if (value === 'device') {
        this.deviceTypeOptions = [
          { value: 'trs_trs_ne', label: '传输网设备' },
          { value: 'AccessNetwork', label: '接入网设备' },
          { value: 'DataNetwork', label: '数据网设备' },
          { value: 'MobileCoreNetwork', label: '移动核心网设备'},
          { value: 'WirelessNetwork', label: '无线网设备'}
        ];
      }
    },
    // 选择设备类型
    onDeviceTypeChange(value) {
      console.log('选择设备类型:', value);
      this.selectedBoard = '';
      this.selectedPort = '';
      if (this.profession == 'space') {
        this.getSelectorData('space', value).then(res => { 
          if (res.data.code === '0000') {
            const { records, total } = res.data.data
            this.cascadeTableData = records
            this.tableDataTotal = total
          }
        });
      }
      // 根据设备类型加载板卡选项
      if (this.selectedProfession === 'device') {
        this.getEquipmentResources('device', value).then(res => { 
          if (res.data.code === '0000') {
            const { records, total } = res.data.data
            this.deviceTableData = records
            this.deviceTableDataTotal = total
          }
        });
      }
    },

    // 选择表格中的项目
    selectTableItem(row) {
      console.log("🚀 ~ selectTableItem ~ row:", row[0])
      console.log('选择表格项:', row[0]);
      // 可以在这里处理选择逻辑，比如高亮显示等
      this.$message.success(`已选择: ${row[0]}`);
    },
    // 查看板卡
    checkBorderCard(row) {
      this.cardRow = row;
      const {eqpId} = row;
      this.getCard(eqpId).then(res => { 
        if (res.data.code === '0000') {
          const { records, total } = res.data.data
          this.cardVisible = true;
          this.cardTableData = records
          this.cardTableDataTotal = total
          if (total == 0) {
            this.$message.error('暂无数据')
          }
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 查看端口
    checkPort(row) {
      this.portRow = row;
      const {cardId} = row;
      this.getPort(cardId).then(res => { 
        if (res.data.code === '0000') {
          const { records, total } = res.data.data
          this.portVisible = true;
          this.portTableData = records
          this.portDataTotal = total
          if (total == 0) {
            this.$message.error('暂无数据')
          }
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 确认类型选择
    confirmTypeSelection() {
      const selection = {
        profession: this.selectedProfession,
        deviceType: this.selectedDeviceType,
        board: this.selectedBoard,
        portVisible: this.selectedPort
      };

      console.log('确认选择:', selection);

      // 构建显示文本
      let displayText = '';
      if (this.selectedProfession) {
        const professionLabel = this.professionOptions.find(item => item.value === this.selectedProfession)?.label;
        displayText += professionLabel;
      }
      if (this.selectedDeviceType) {
        const deviceTypeLabel = this.deviceTypeOptions.find(item => item.value === this.selectedDeviceType)?.label;
        displayText += ` > ${deviceTypeLabel}`;
      }

      // 只有在选择设备资源时才显示板卡和端口
      if (this.selectedProfession === 'device') {
        if (this.selectedBoard) {
          const boardLabel = this.boardOptions.find(item => item.value === this.selectedBoard)?.label;
          displayText += ` > ${boardLabel}`;
        }
        if (this.selectedPort) {
          const portLabel = this.portOptions.find(item => item.value === this.selectedPort)?.label;
          displayText += ` > ${portLabel}`;
        }
      }

      // 更新输入框显示
      this.form.pointType = displayText;

      this.showTypeDialog = false;
    },
    // 处理分页
    handleTableDataCurrentChange(val){
      this.tableDataPageNum = val;
      // this.getSelectorData('space', this.selectedProfession);
    },
    handleDeviceTableDataCurrentChange(val) {
      this.deviceTableDataPageNum = val;
      // this.getEquipmentResources('device', this.selectedDeviceType);
    },
    handleCardTableDataCurrentChange(val) {
      this.cardTableDataPageNum = val;
      // this.getEquipmentResources('device', this.selectedDeviceType);
    },
    handlePortTableDataCurrentChange(val) {
      this.portTableDataPageSize = val;
      // this.getEquipmentResources('device', this.selectedDeviceType);
    },
    // 锁定起点和终点位置
    lockEndpoints() {
      if (!this.selectedPolyline || this.isLockingEndpoints) return;

      this.isLockingEndpoints = true; // 设置锁定标志

      const path = this.selectedPolyline.getPath();
      let pathChanged = false;

      // 确保起点位置不变
      if (path.length > 0 && !this.pointsEqual(path[0], this.originalStart)) {
        path[0] = this.originalStart;
        pathChanged = true;
      }

      // 确保终点位置不变
      if (path.length > 1 && !this.pointsEqual(path[path.length - 1], this.originalEnd)) {
        path[path.length - 1] = this.originalEnd;
        pathChanged = true;
      }

      // 只在路径变化时更新
      if (pathChanged) {
        try {
          // 临时禁用事件监听
          if (this.selectedPolyline.disableEventListeners) {
            this.selectedPolyline.disableEventListeners();
          }

          this.selectedPolyline.setPath(path);
        } finally {
          // 重新启用事件监听
          if (this.selectedPolyline.enableEventListeners) {
            this.selectedPolyline.enableEventListeners();
          }
        }
      }

      this.isLockingEndpoints = false; // 清除锁定标志
    },
    // 比较两个点是否相同
    pointsEqual(point1, point2) {
      return point1.lng === point2.lng && point1.lat === point2.lat;
    },
    // 结束编辑模式
    endEditMode() {
      if (this.currentEditingPolyline) {
        this.currentEditingPolyline.setEditing(false);
        this.clearPolylineListeners();
        this.currentEditingPolyline = null;
      }
    },
    // 清除折线的事件监听器
    clearPolylineListeners() {
      if (this.currentEditingPolyline) {
        this.currentEditingPolyline.removeEventListener('lineupdate', this.throttledUpdatePolyline);
        this.currentEditingPolyline.removeEventListener('removevertex', this.updatePolyline);
        this.currentEditingPolyline.removeEventListener('addvertex', this.updatePolyline);
        this.currentEditingPolyline.removeEventListener('click', this.endEditMode);
      }
    },

    // 强制地图重绘
    forceMapRedraw() {
      // if (this.map) {
      //   const zoom = this.map.getZoom();
      //   this.map.setZoom(zoom + 0.000001);  // 微调缩放级别强制重绘
      //   setTimeout(() => this.map.setZoom(zoom), 10);
      // }
    },

    // 节流更新函数，避免频繁触发
    throttledUpdatePolyline: (function () {
      let timer = null;
      return function () {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          this.updatePolyline();
          timer = null;
        }, 100);
      };
    })(),

    // 更新折线数据
    updatePolyline() {
      // 确保端点位置正确
      this.lockEndpoints();
      this.forceMapRedraw();
      const path = this.selectedPolyline.getPath();
      const points = path.map(point => ({
        lng: point.lng,
        lat: point.lat
      }));
      // console.log(this.convertCoordinates(points));

      // console.log('折线已更新:', points,this.nowChangeLineData);
      let queryData = {
        lineId: this.nowChangeLineData.lineId,
        lineStrBGd09: this.convertCoordinates(points),
        busType: this.nowChangeLineData.busType,
        lineColor: this.nowChangeLineData.lineColor,
        lineType: this.nowChangeLineData.lineType,
        lineName: this.nowChangeLineData.lineName
      };
      this.$axios.put(`/protect-api/pointLine/addPointLineStr`, queryData).then((data) => {
        if (data.data.code === "0000") {
        }
      });
      // 这里可以添加实际更新数据的逻辑
      // 例如发送数据到服务器或更新UI
    },
    convertCoordinates(jsonData, precision = 8) {
      if (!Array.isArray(jsonData) || jsonData.length === 0) {
        return '';
      }

      // 转换每个坐标点并拼接成字符串
      return jsonData.map(point => {
        // 处理经度
        const lng = Number(point.lng).toFixed(precision);
        // 处理纬度
        const lat = Number(point.lat).toFixed(precision);

        return `${lng},${lat}`;
      }).join(';');
    },
    async downloadTemplate(title, downloadUrl) { // 重命名参数避免冲突
      let url = null; // 提前声明URL变量
      let loading = null;

      try {
        // 显示加载状态
        loading = this.$loading({
          lock: true,
          text: '正在下载模板...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 发送请求获取文件
        // console.log(title, downloadUrl); // 使用新的参数名
        const response = await fetch(downloadUrl);

        if (!response.ok) {
          throw new Error(`下载失败: ${response.statusText}`);
        }

        // 获取文件内容并创建Blob对象
        const blob = await response.blob();

        // 创建下载链接 - 使用提前声明的url变量
        url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;

        // 自定义文件名
        a.download = title;

        // 触发下载
        document.body.appendChild(a);
        a.click();

      } catch (error) {
        console.error('下载模板出错:', error);
        this.$message({
          message: '模板下载失败，请稍后重试',
          type: 'error'
        });
      } finally {
        // 清理资源
        if (url) URL.revokeObjectURL(url);
        if (loading) loading.close();

        // 确保移除创建的DOM元素
        const aTag = document.querySelector(`a[download="${title}"]`);
        if (aTag) document.body.removeChild(aTag);
      }
    },
    goRouterEdit(circuitId) {
      if (!circuitId) return;
      // 通过ID查找对应的item
      const selectedItem = this.latitudeData.find(item => item.circuitId === circuitId);
      if (!selectedItem) {
        console.warn('未找到对应的电路信息');
        return;
      }
      const params = {
        classId: 'trsCircuit',
        circuitNo: selectedItem.circuitNo,
      };

      // 拼接参数到URL
      const queryString = new URLSearchParams(params).toString();
      const url = `http://***********:9183/#/resource?${queryString}`;

      // 打开链接
      window.open(url, '_blank');
    },
    // 选择地址
    selectAddress(type, item) {
      console.log(item)
      this.routerForm[type].pointName = item.address;
      // 这里可以根据需要设置其他地址相关信息
      this.showDropdown = ''; // 隐藏下拉框
      // 初始化数组，确保始终有2个元素


      this.setPointByCoordinates(item.location.x, item.location.y)

    },
    // 点击其他地方时隐藏下拉框
    handleClickOutside() {
      this.showDropdown = '';
    },
    /**
   * 通过坐标直接设置起点或终点
   * @param {number} lat 纬度
   * @param {number} lng 经度
   */
    setPointByCoordinates(lng, lat) {

      // 创建百度地图点对象
      lng = lng.toFixed(8)
      lat = lat.toFixed(8)
      const point = new BMapGL.Point(lng, lat); // 确保是数值类型
      console.log(point)
      // 清除旧标记
      const markerType = this.routeSelectionStep === 1 ? 'startMarker' : 'endMarker';
      if (this[markerType]) {
        this.map.removeOverlay(this[markerType]);
      }
      // 创建标记点
      const newMarker = new BMapGL.Marker(point, {
        icon: new BMapGL.Icon('http://**********:9000/important-protection-test/2025/06/13/0e8a696085dd49c7af68f6a0acb7a691.png', new BMapGL.Size(26, 42), {
          anchor: new BMapGL.Size(13, 42)
        })
      });

      // 保存引用
      this.map.panTo(point)
      this[markerType] = newMarker;
      this.map.addOverlay(newMarker);
      // 获取点击点位的地址信息
      this.$axios.post(`/protect-api/map/longitudeToAddress?lat=${lat}&lng=${lng}`).then((data) => {
        if (data.data.code === "0000") {
          let result = data.data.data.UNI_BSS_BODY.REVERSE_GEOCODING_RSP.result;
          const address = (result.formatted_address || result.sematic_description);
          if (this.routeSelectionStep === 1) {
            // 显示信息窗口
            const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `, {
              offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
            });

            this.map.openInfoWindow(infoWindow, point);
            this.routerForm.startPoint.pointName = (result.formatted_address || result.sematic_description);
            this.routerForm.startPoint.longitude = point.lng.toFixed(8);
            this.routerForm.startPoint.latitude = point.lat.toFixed(8);
            this.routerForm.startPoint.pointAlias = (result.formatted_address || result.sematic_description);
            this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
            if (this.routerForm.endPoint.longitude && this.routerForm.endPoint.latitude) {
              this.connectStartAndEndPoints();
            }
            // this.map.removeEventListener('click', this.handleMapClick);
            // this.routerForm.startPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
          } else if (this.routeSelectionStep === 2) {
            // 显示信息窗口
            const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `, {
              offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
            });
            this.map.openInfoWindow(infoWindow, point);
            this.routerForm.endPoint.pointName = (result.formatted_address || result.sematic_description);
            this.routerForm.endPoint.longitude = point.lng.toFixed(8);
            this.routerForm.endPoint.latitude = point.lat.toFixed(8);
            this.routerForm.endPoint.pointAlias = (result.formatted_address || result.sematic_description);
            this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
            // 当终点选择完成后，连接起点和终点
            if (this.routerForm.startPoint.longitude && this.routerForm.startPoint.latitude) {
              this.connectStartAndEndPoints();
            }
            // this.map.removeEventListener('click', this.handleMapClick);
            // this.routerForm.endPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
          } else {
            // 未明确选择起点/终点时，提示用户
            this.$message({
              message: '请先点击"新增线路"按钮开始选择起点和终点',
              type: 'info'
            });
            // this.routerForm.lineName = this.routerForm.startPoint.pointName+' / '+this.routerForm.endPoint.pointName;
          };
        } else {
          let result = {
            "location": {
              "lng": lng,
              "lat": lat
            },
            "formatted_address": "陕西省西安市雁塔区等驾坡街道田马路",
            "edz": {
              "name": "曲江新区"
            },
            "business": "",
            "business_info": [],
            "addressComponent": {
              "country": "中国",
              "country_code": 0,
              "country_code_iso": "CHN",
              "country_code_iso2": "CN",
              "province": "陕西省",
              "city": "西安市",
              "city_level": 2,
              "district": "雁塔区",
              "town": "等驾坡街道",
              "town_code": "610113005",
              "distance": "",
              "direction": "",
              "adcode": "610113",
              "street": "田马路",
              "street_number": ""
            },
            "pois": [],
            "roads": [],
            "poiRegions": [],
            "sematic_description": "",
            "formatted_address_poi": "",
            "cityCode": 233
          };

          if (this.routeSelectionStep === 1) {
            const address = '起点';
            // 显示信息窗口
            const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `, {
              offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
            });
            this.map.openInfoWindow(infoWindow, point);
            this.routerForm.startPoint.pointName = address;
            this.routerForm.startPoint.longitude = point.lng.toFixed(8);
            this.routerForm.startPoint.latitude = point.lat.toFixed(8);
            this.routerForm.startPoint.pointAlias = address;
            this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
            if (this.routerForm.endPoint.longitude && this.routerForm.endPoint.latitude) {
              this.connectStartAndEndPoints();
            }
            // this.routerForm.startPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
          } else if (this.routeSelectionStep === 2) {
            // 显示信息窗口
            const address = '终点';
            const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `, {
              offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
            });
            this.map.openInfoWindow(infoWindow, point);
            this.routerForm.endPoint.pointName = address;
            this.routerForm.endPoint.longitude = point.lng.toFixed(8);
            this.routerForm.endPoint.latitude = point.lat.toFixed(8);
            this.routerForm.endPoint.pointAlias = address;
            this.routerForm.lineName = this.routerForm.startPoint.pointName + ' / ' + this.routerForm.endPoint.pointName;
            // 当终点选择完成后，连接起点和终点
            if (this.routerForm.startPoint.longitude && this.routerForm.startPoint.latitude) {
              this.connectStartAndEndPoints();
            }
            // this.map.removeEventListener('click', this.handleMapClick);
            // this.routerForm.endPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
          } else {
            // 未明确选择起点/终点时，提示用户
            this.$message({
              message: '请先点击"新增线路"按钮开始选择起点和终点',
              type: 'info'
            });
            // this.routerForm.lineName = this.routerForm.startPoint.pointName+' / '+this.routerForm.endPoint.pointName;
          };
        }
      });
    }

  },

};
</script>




<style lang="less" scoped>
#allmap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  font-family: "微软雅黑";
}

.numberStyle {
  color: red;
}

.fixed-search-box {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  box-shadow: 0 2Px 10Px 0 rgb(0 0 0 / 10%);
}

.header-long {
  position: fixed;
  width: 100%;
  height: 70Px;
  line-height: 70Px;
  z-index: 2;
  background-image: url('../assets/img_slices/bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.header-right-middle {
  position: fixed;
  width: 100%;
  height: 63Px;
  text-align: center;
  // background-image: url('../assets/zxx/middleText2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 36Px;
  font-weight: 600;
  font-family: Alimama ShuHeiTi;
  color: #DAFFFC;
}

.header-right-middle-span {
  width: 356Px;
  height: 34Px;
  font-size: 36Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #C6F6FF;
  line-height: 50Px;
  opacity: 0.89;
  text-shadow: 0Px 4Px 6Px rgba(7, 46, 26, 0.7);

  background: linear-gradient(0deg, rgba(119, 255, 253, 0.45) 0%, rgba(233, 248, 255, 0.45) 73.3154296875%, rgba(255, 255, 255, 0.45) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

}

.imgLT {
  background-image: url('../assets/zxx/lt.png');
  background-repeat: no-repeat;
  background-size: 81% 81%;
  width: 84Px;
  height: 46Px;
  margin: 4Px 10Px;
}

.dateSpan {
  margin-left: 1740Px;
  margin-top: 12Px;
  position: absolute;
}

.date {
  text-align: left;
  width: 200Px;
  line-height: 14Px;
  color: #07697f;
  font-size: 14Px;
}

// .buttonZ1 {
//   z-index: 2;
//   // position: fixed;
//   // margin-left: 678Px;
//   // margin-top: 88Px;
//   // width: 126Px;
//   float: left;
//   margin-right: 10Px;
//   width: 10%;
//   height: 38Px;
//   box-shadow: 0 0 2Px #107093;
//   text-align: center;
//   background-image: url('../assets/zxx/button2.png');
//   background-repeat: no-repeat;
//   background-size: 100% 100%;
// }

// .buttonZ2 {
//   z-index: 2;
//   // position: fixed;
//   // margin-left: 819Px;
//   // margin-top: 88Px;
//   // width: 126Px;
//   float: left;
//   margin-right: 10Px;
//   width: 10%;
//   box-shadow: 0 0 2Px #107093;
//   height: 38Px;
//   text-align: center;
//   background-image: url('../assets/zxx/button2.png');
//   background-repeat: no-repeat;
//   background-size: 100% 100%;
// }

// .buttonZ3 {
//   z-index: 2;
//   // position: fixed;
//   // margin-left: 959Px;
//   // margin-top: 88Px;
//   // width: 136Px;
//   margin-right: 10Px;
//   float: left;
//   width: 10%;
//   height: 38Px;
//   box-shadow: 0 0 2Px #107093;
//   text-align: center;
//   background-image: url('../assets/zxx/button2.png');
//   background-repeat: no-repeat;
//   background-size: 100% 100%;
// }
.buttonZ1,
.buttonZ2,
.buttonZ3 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 10%;
  height: 38Px;
  line-height: 38Px;
  float: left;
  /* 水平排列容器 */
  margin-right: 20px;
  /* 容器间间距 */
  box-shadow: 0 0 2Px #107093;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonZ4 {
  z-index: 2;
  position: fixed;
  margin-left: 1109Px;
  margin-top: 88Px;
  box-shadow: 0 0 2Px #107093;
  width: 136Px;
  height: 38Px;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonSpanCur {
  z-index: 2;
  // width: 82Px;
  cursor: pointer;
  // height: 19Px;
  font-size: 20Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #DAFFFC;
  line-height: 35Px;
  /*  background: linear-gradient(0deg, rgba(239,251,255,0.34) 0%, rgba(0,0,0,0.34) 11.9140625%, rgba(255,254,254,0.34) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;*/
}

.buttonSpan {
  cursor: pointer;
  // width: 82Px;
  height: 19Px;
  font-size: 20Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #8DE8EE;
  line-height: 35Px;
  opacity: 0.85;
}

.mapLeftTop1 {
  z-index: 2;
  // position: fixed;
  // margin-left: 32Px;
  // margin-top: 191Px;
  width: 420Px;
  height: 180Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

/*滚动条样式*/
.mapLeftTop1div::-webkit-scrollbar {
  width: 4px;
  /*height: 4px;*/
}

.mapLeftTop1div::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.mapLeftTop1div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

.mapLeftTopTitle {
  margin-left: 43Px;
  margin-top: 99Px;
  width: 95Px;
  height: 25Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 44Px;
  text-shadow: 0Px 2Px 8Px rgba(14, 197, 236, 0.36);
}

.mapRightTopTitle {
  z-index: 2;
  width: 18Px;
  height: 18Px;
  line-height: 44Px;
  margin: 13Px 13Px;
  float: right;
  background-image: url('../assets/zxx/reset3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  transition: all 0.3s ease;
}

.mapRightTopTitle:hover {
  transform: rotate(360deg) scale(2.0);

  -webkit-transform: rotate(360deg) scale(2.0);

  -moz-transform: rotate(360deg) scale(2.0);

  -o-transform: rotate(360deg) scale(2.0);

  -ms-transform: rotate(360deg) scale(2.0);
}

.mapRightTopTitle:active {
  transform: scale(0.8);
}

.mapLeftTop1div {
  margin-left: 16Px;
  margin-top: 25Px;
  width: 388Px;
  height: 97Px;
  overflow: auto;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;
}

.mapLeftTop1Radis {
  width: 8Px;
  height: 8Px;
  margin-left: 16Px;
  background: #39D4CD;
  border-radius: 50%;
  display: inline-block;
}

.mapLeftTopName {
  width: 100%;
  // height: 100px;
  font-size: 15Px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #39D4CD;
  line-height: 35Px;
  margin-left: 7Px;
}

.mapLeftTopText {
  width: 335Px;
  height: 50Px;
  font-size: 14Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 20Px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.mapLeftTop2 {
  z-index: 2;
  // position: fixed;
  // float: left;
  // margin-left: 32Px;
  // margin-top: 397Px;
  width: 420Px;
  height: 390Px;
  /*  height: 282Px;*/
  background-image: url('../assets/zxx/mapLeftTop23.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Img1 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Img2 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Text {
  margin-top: 41Px;
  margin-left: 45Px;
  width: 75Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
  display: inline-block;
}

.mapLeftTop2Text2 {
  position: absolute;
  margin-left: 200Px;
  margin-top: 27Px;
  width: 62Px;
  height: 27Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  color: #FABB61;
  line-height: 50Px;
  display: inline-block;
}

.mapLeftTop2Text3 {
  position: absolute;
  width: 62Px;
  height: 27Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  color: #FABB61;
  line-height: 50Px;
  display: inline-block;
}

.mapLeftTop2Div {
  width: 388Px;
  height: 97Px;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;
}

.mapLeftTop3 {
  z-index: 2;
  // position: fixed;
  // margin-left: 32Px;
  // margin-top: 812Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapLeftTop2Img3 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Text2Top {
  width: 140Px;
  height: 24Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  text-align: center;
  /*  color: #2DC3BA;*/
  position: absolute;
}

.mapLeftTop2Text2Bottom {
  width: 140Px;
  height: 38Px;
  font-size: 14Px;
  text-align: center;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
}

.mapRightTop1 {
  z-index: 2;
  // position: fixed;
  // margin-left: 1473Px;
  // margin-top: 617Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapRightTop2 {
  z-index: 2;
  // position: fixed;
  // margin-left: 1473Px;
  // margin-top: 815Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapRightTop3 {
  z-index: 2;
  // position: fixed;
  // margin-left: 1473Px;
  // margin-top: 180Px;
  width: 420Px;
  height: 420Px;
  background-image: url('../assets/zxx/glbg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}


.mapLeftTopTitleDetalis {
  margin-left: 4Px;
  margin-top: 15Px;
  width: 17Px;
  height: 16Px;
  background-image: url('../assets/zxx/details.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  position: absolute;
}

/deep/.BMap_bubble_pop {
  background-color: transparent;
}

.mapLeftTopz {
  z-index: 3;
  position: fixed;
  margin-left: 32Px;
  margin-top: 5%;
  width: 420Px;
  height: 50Px;
}

.left {
  z-index: 11;
  width: 20Px;
  height: 20Px;
  background-image: url('../assets/zxx/details.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  float: left;
  margin: 12Px 12Px;
}

.right {
  z-index: 11;
  width: 20Px;
  height: 20Px;
  background-image: url('../assets/zxx/left.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  float: right;
  margin: 12Px 12Px;
}

.topologyBackground {
  width: 100%;
  height: 100%;
  z-index: 1;
  width: 1920Px;
  height: 1080Px;
  position: relative;
  background-image: url('../assets/zxx/topologyBack.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.topology {
  z-index: 1;
  width: 100%;
  // margin: 0 auto;
  height: 100%;
  display: flex;
  justify-content: center;
  // background-image: url('../assets/zxx/topology.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  // position: absolute;

}

.item-list {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.item-list2 {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
}

.item {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-right: .2rem;
  // color: #333;
  // line-height: .17rem;
  overflow: hidden;
  width: 100%;
  height: 7.35rem;
  // width: 388Px;
  // height: 97Px;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;

  .mapLeftTop1Radis {
    width: 8Px;
    height: 8Px;
    margin-left: 16Px;
    background: rgba(252, 164, 58, 1);
    border-radius: 50%;
    display: inline-block;
  }

  .mapLeftTop1Text {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    // line-height: 50px;
  }

  .mapLeftTopText {
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
  }

}

.fix_content {
  position: absolute;
  width: 98%;
  height: 310px;
  overflow: hidden;
  top: 53px;
  left: 0;
}

.alog {
  width: 100%;
  height: 53%;
}

/* 定义滚动条样式 */
/deep/.contont::-webkit-scrollbar {
  width: 3px;
  height: 3px;
  background-color: rgba(240, 240, 240, 1);
}

/deep/.contont::-webkit-scrollbar:hover {
  width: 6px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/.contont::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/.contont:hover::-webkit-scrollbar-corner {
  width: 6px;
  border: 5px solid #06363b;
  background-color: #06363b;
  /*background-color: red !important;*/
}

/*定义滑块 内阴影+圆角*/
/deep/.contont::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #317179;
  background-color: #317179;
}

/deep/.contont::-webkit-scrollbar-thumb:hover {
  border: 5px solid #06363b;
  background-color: #06363b;
}

.mapRightTop1Img1 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapRightTop1Img2 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapRightTop1Img3 {
  margin-top: 17Px;
  margin-left: 147Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

/deep/.stDataDiv1 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #4FA1FF 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv2 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #306747 0%, #47F195 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(133, 255, 189, 0.5), rgba(90, 219, 149, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv3 {
  width: 141px;
  height: 40px;
  display: inline-block;
  background: linear-gradient(-51deg, #734c1c 0%, #ECB26A 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(253, 215, 169, 0.5), rgba(255, 172, 71, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv4 {
  width: 130px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #4FA1FF 100%);
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
  border-radius: 20px;
}

/deep/.stDataDiv5 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #1668c4 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv6 {
  width: 141px;
  height: 40px;
  display: inline-block;
  background: linear-gradient(-51deg, #734c1c 0%, #ec9a37 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(253, 215, 169, 0.5), rgba(255, 172, 71, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataSpan1 {
  width: 91px;
  height: 18px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #091220;
  display: inline-block;
  line-height: 40px;
}

/deep/.stDataSpan4 {
  width: 91px;
  height: 18px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  background: transparent;
  color: #55A4FE;
  display: inline-block;
  line-height: 40px;
}

.noticePopover {
  z-index: 6;
  margin-left: 560Px;
  margin-top: -730Px;
  position: absolute;
  width: 800Px;
  height: 312px;
  background-image: url('../assets/zxx/noticePopover.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.alarmPopover {
  z-index: 6;
  margin-left: 500Px;
  margin-top: -730Px;
  position: absolute;
  width: 929Px;
  height: 450Px;
  background-image: url('../assets/zxx/alarm.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.noticePopoverTitle {
  position: relative;
  margin-top: 17Px;
  margin-left: 35Px;
  width: 198Px;
  height: 19Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 50Px;
}

.alarmPopoverTitle {
  position: relative;
  margin-top: 7Px;
  margin-left: 35Px;
  width: 900Px;
  height: 19Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 50Px;
}

.alarmPopoverText4 {
  position: relative;
  text-align: center;
  width: 900Px;
  font-size: 16Px;
  margin-top: 25Px;
  margin-left: 18Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}

.alarmPopoverX {
  z-index: 6;
  position: fixed;
  margin-top: 0Px;
  margin-left: 883Px;
  width: 14Px;
  height: 14Px;
  cursor: pointer;
  background-image: url('../assets/zxx/X.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.noticePopoverX {
  z-index: 6;
  position: fixed;
  margin-top: 0Px;
  margin-left: 750Px;
  width: 14Px;
  height: 14Px;
  cursor: pointer;
  background-image: url('../assets/zxx/X.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.noticePopoverText {
  position: relative;
  margin-top: 10Px;
  margin-left: 30Px;
  width: 757Px;
  min-height: 88Px;
  max-height: 190Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 24Px;
  overflow-y: auto;
}

.noticePopoverText2 {
  position: relative;
  /*  margin-top: 12Px;*/
  text-align: right;
  font-size: 16Px;
  width: 770Px;
  height: 24Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 24Px;
}

.noticePopoverText3 {
  position: relative;
  text-align: right;
  width: 770Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}

.noticePopoverText4 {
  position: relative;
  text-align: center;
  width: 770Px;
  font-size: 16Px;
  margin-top: 46Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}

/* 定义滚动条样式 */
/deep/.noticePopoverText::-webkit-scrollbar {
  width: 3px;
  height: 3px;
  background-color: rgba(240, 240, 240, 1);
}

/deep/.noticePopoverText::-webkit-scrollbar:hover {
  width: 6px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/.noticePopoverText::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/.noticePopoverText:hover::-webkit-scrollbar-corner {
  width: 6px;
  border: 5px solid #06363b;
  background-color: #06363b;
  /*background-color: red !important;*/
}

/*定义滑块 内阴影+圆角*/
/deep/.noticePopoverText::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #317179;
  background-color: #317179;
}

/deep/.noticePopoverText::-webkit-scrollbar-thumb:hover {
  border: 5px solid #1f5c65;
  background-color: #1f5c65;
}

.el-button.is-plain:focus,
.el-button.is-plain:hover {
  // width: 58px;
  height: 32Px;
  font-size: 14Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #2AB8B7;
  line-height: 29Px;
  padding: 0px;
  padding-left: 10px;
  padding-right: 10px;
  /*      padding-top: 10px;*/
  padding-bottom: 10px;
  border: 1px solid #1a9695;
  background-color: #038793;
  box-shadow: 0 0 5px #1a9695;
  color: #fff;
}

.route-selector-tip {
  position: absolute;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 线路命名输入框样式 */
.route-naming {
  // position: absolute;
  // top: 100px;
  // left: 50%;
  // transform: translateX(-50%);
  // z-index: 99;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 4px;
  // display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  /deep/.el-form-item--mini .el-form-item__label {
    color: #fff !important;
  }

  .el-input {
    width: 60%;
  }

  .address-dropdown {
    position: absolute;
    z-index: 999;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .dropdown-item {
    padding: 8px 15px;
    cursor: pointer;
    color: #000;
  }

  .dropdown-item:hover {
    background-color: #f5f7fa;
  }
}

.search-results-container {
  position: absolute;
  top: 50px;
  left: 20px;
  width: 300px;
  max-height: 500px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #f5f7fa;
  background-color: #fafafa;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.search-results-header span {
  font-weight: bold;
  color: #303133;
}

.search-results-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.search-loading,
.search-error,
.search-no-results {
  padding: 15px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.search-results-list {
  padding: 0 10px;
}

.search-result-item {
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 5px;
  transition: background-color 0.2s;
}

.search-result-item:hover,
.search-result-item.active {
  background-color: #f5f7fa;
}

.result-title {
  font-weight: bold;
  color: #606266;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.result-address {
  font-size: 12px;
  color: #606266;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
}

.result-coordinates {
  font-size: 12px;
  color: #909399;
}

.search-results-footer {
  padding: 8px 15px;
  border-top: 1px solid #f5f7fa;
  background-color: #fafafa;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.drawing-panel {
  z-index: 999;
  position: fixed;
  bottom: 3.5rem;
  margin-left: 2.5rem;
  padding-left: 0;
  border-radius: .25rem;
  height: 47Px;
  box-shadow: 0 2px 6px 0 rgba(27, 142, 236, 0.5);
}

.bmap-btn {
  border-right: 1px solid #d2d2d2;
  float: left;
  width: 64Px;
  height: 100%;
  background-image: url(//api.map.baidu.com/library/DrawingManager/1.4/src/bg_drawing_tool.png);
  cursor: pointer;
}

.drawing-panel .bmap-marker {
  background-position: -65Px 0;
}

.drawing-panel .bmap-polyline {
  background-position: -195Px 0;
}

.drawing-panel .bmap-rectangle {
  background-position: -325Px 0;
}

.drawing-panel .bmap-polygon {
  background-position: -260Px 0;
}

.drawing-panel .bmap-circle {
  background-position: -130Px 0;
}

.el-select {
  width: 100%;
}

.upload-preview {
  margin-top: 10px;
  position: relative;
  display: inline-block;
}

.thumbnail-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.thumbnail-image:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  opacity: 0.8;
}

.remove-btn:hover {
  opacity: 1;
}

.upload-progress {
  margin-top: 8px;
  width: 100%;
}

/deep/.el-dialog {
  margin-top: 7vh !important;
}
// /deep/ .setupDialog .el-dialog {
//   margin-top: 10vh !important;
//   margin-right: 5vw !important;
// }

/deep/.el-dialog__body {
  padding: 10px 20px;
  height: 70vh;
  overflow: auto;
}
/deep/.setupDialog .el-dialog__body {
  padding: 10px 20px;
  height: 50vh;
  overflow: auto;
}


/deep/.el-color-picker--mini .el-color-picker__trigger {
  width: 200px;
}

.bg-black {
  /deep/ .el-input__inner {
    height: 30px;
    width: 95%;
    // background: radial-gradient(black, transparent);
    background: transparent;
    border: #eee solid .5px;
    color: #fff;
    margin: 5px;
  }
}

.back_table {
  background: radial-gradient(black, transparent);

  /deep/.el-table {
    color: #fff;
    background: transparent;

  }

  /deep/.el-table tr {
    background-color: transparent;
  }

  /deep/.el-table__body tr.hover-row>td.el-table__cell {
    background-color: transparent;
  }

  /deep/.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    background-color: transparent;
  }

  /deep/.el-table__fixed-right-patch {
    background-color: transparent;
  }

  /deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: #00ffff21;
  }

  /deep/.el-table__expanded-cell {
    background-color: transparent;
  }

  /deep/.el-table td.el-table__cell,
  /deep/.el-table th.el-table__cell.is-leaf {
    border: none;
  }

  /deep/.el-table thead {
    color: #fff
  }

  /deep/.el-table th.el-table__cell {
    background-color: #00f3da21;
  }
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}

.contextmenu li:hover {
  background: #eee;
}

.alarm {
  /deep/.el-table .el-table__cell.gutter {
    width: 0px !important;
    height: 0px;
    display: none;
  }

  /deep/ .el-table__body-wrapper::-webkit-scrollbar:hover {
    width: 8px !important;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  /deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
    border-radius: 10px;
    background-color: rgba(240, 240, 240, .5);
  }

  /deep/ .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
    width: 6px;
  }

  /*定义滑块 内阴影+圆角*/
  /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 0px #206977;
    background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
  }

  /deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 5px; // 横向滚动条
    height: 5px; // 纵向滚动条 必写
  }

  /deep/ .el-pagination.is-background .el-pager li {
    background-color: transparent;
  }

  /deep/ .el-table td.el-table__cell div {
    color: #E1E1E1;
  }

  /deep/.el-table,
  .el-table__expanded-cell {
    background-color: transparent;
  }

  /deep/.el-table tr th.el-table__cell>.cell {
    font-size: 16Px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #2AB8B7;
    line-height: 38Px;
  }

  /deep/.el-table .cell {
    font-size: 15Px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #2AB8B7;
    line-height: 36Px;
  }

  /deep/.el-table .el-table__cell {
    padding: 2Px 0;
    min-width: 0;
    box-sizing: border-box;
    text-overflow: ellipsis;
    vertical-align: middle;
    position: relative;
    text-align: left;
  }

  /deep/.el-table tr {
    background-color: transparent;
  }

  /deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: transparent !important;
  }

  /deep/ .el-table .warning-row {
    background: transparent !important;
  }

  /deep/.el-table .success-row {
    background: #094a57 !important;
  }

  /deep/.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    background-color: #082a37FF !important;

  }

  /deep/.el-table--enable-row-hover .el-table__body tr:hover {
    background-color: transparent !important;
  }

  /deep/.el-table--enable-row-transition .el-table__body td.el-table__cell {
    border: none;
  }

  /deep/.el-table th.el-table__cell.is-leaf {
    border: none;
    border-bottom: none;
  }

  .el-pagination {
    float: right;
  }

  /deep/ .el-table__body-wrapper::-webkit-scrollbar:hover {
    width: 8px !important;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  /deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
    border-radius: 10px;
    background-color: rgba(240, 240, 240, .5);
  }

  /deep/ .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
    width: 6px;
  }

  /*定义滑块 内阴影+圆角*/
  /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 0px #206977;
    background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
  }

  /deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 5px; // 横向滚动条
    height: 5px; // 纵向滚动条 必写
  }
}
.tab-label {
  display: inline-block;
  max-width: 150px;
  /* 根据需要调整 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

/* 类型选择弹出框样式 */
.type-selection-container {
  padding: 10px;
}

.selection-area {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.selection-item {
  margin-bottom: 15px;
}

.selection-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #606266;
}

.selection-item .el-select {
  width: 100%;
}

.table-area {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 15px;
}

.table-area .el-table {
  border-radius: 4px;
}
</style>
